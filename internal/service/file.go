package service

import (
	"context"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"

	gqlmodel "gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/graph/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/model"
)

type FileService struct {
	fileRepo   FileRepositoryInterface
	r2Service  R2ServiceInterface
	cdnService *CDNService
	config     *config.Config
}

func NewFileService(fileRepo FileRepositoryInterface, r2Service R2ServiceInterface, cdnService *CDNService, cfg *config.Config) *FileService {
	return &FileService{
		fileRepo:   fileRepo,
		r2Service:  r2Service,
		cdnService: cdnService,
		config:     cfg,
	}
}

// UploadFile handles file upload (both direct and signed URL)
func (s *FileService) UploadFile(ctx context.Context, input *gqlmodel.UploadInput, userID *string) (*gqlmodel.UploadResponse, error) {
	// Validate input
	if err := s.validateUploadInput(input); err != nil {
		return &gqlmodel.UploadResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	// Generate unique filename
	fileID := uuid.New().String()
	ext := filepath.Ext(input.Filename)
	if ext == "" {
		// Try to get extension from mime type
		ext = s.getExtensionFromMimeType(input.MimeType)
	}
	uniqueFilename := fmt.Sprintf("%s%s", fileID, ext)

	// Create file metadata (internal model)
	fileMetadata := &model.File{
		ID:           fileID,
		Filename:     uniqueFilename,
		OriginalName: input.Filename,
		FileType:     string(input.FileType),
		MimeType:     input.MimeType,
		Size:         int64(input.Size),
		Status:       "UPLOADING",
		Tags:         input.Tags,
		Metadata:     input.Metadata,
		UserID:       userID,
	}

	// Generate URLs
	publicURL := s.r2Service.GetPublicURL(uniqueFilename)
	fileMetadata.PublicURL = &publicURL

	// Use CDN service to get optimized CDN URL if available
	if s.cdnService != nil {
		cdnURL := s.cdnService.GetCDNURL(publicURL)
		fileMetadata.CDNURL = &cdnURL
	} else if s.config.CDN.BaseURL != "" {
		cdnURL := fmt.Sprintf("%s/%s", s.config.CDN.BaseURL, uniqueFilename)
		fileMetadata.CDNURL = &cdnURL
	}

	// Save to database
	if err := s.fileRepo.Create(ctx, fileMetadata); err != nil {
		return &gqlmodel.UploadResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to save file metadata: %v", err),
		}, nil
	}

	// Convert to GraphQL model
	gqlFile := s.convertToGQLFileMetadata(fileMetadata)
	response := &gqlmodel.UploadResponse{
		Success: true,
		Message: "File metadata created successfully",
		File:    gqlFile,
	}

	// Generate signed upload URL if requested
	if input.UseSignedUpload != nil && *input.UseSignedUpload {
		uploadURL, err := s.r2Service.GeneratePresignedUploadURL(
			ctx,
			uniqueFilename,
			input.MimeType,
			time.Duration(s.config.Upload.SignedURLExpiry)*time.Second,
		)
		if err != nil {
			return &gqlmodel.UploadResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to generate upload URL: %v", err),
			}, nil
		}
		response.UploadURL = &uploadURL
		response.Message = "Signed upload URL generated successfully"
	}

	return response, nil
}

// convertToGQLFileMetadata converts internal File model to GraphQL FileMetadata
func (s *FileService) convertToGQLFileMetadata(file *model.File) *gqlmodel.FileMetadata {
	return &gqlmodel.FileMetadata{
		ID:           file.ID,
		Filename:     file.Filename,
		OriginalName: file.OriginalName,
		FileType:     gqlmodel.FileType(file.FileType),
		MimeType:     file.MimeType,
		Size:         int(file.Size),
		Status:       gqlmodel.FileStatus(file.Status),
		UploadedAt:   file.UploadedAt.Format(time.RFC3339),
		UpdatedAt:    file.UpdatedAt.Format(time.RFC3339),
		PublicURL:    file.PublicURL,
		CdnURL:       file.CDNURL,
		Tags:         file.Tags,
		Metadata:     file.Metadata,
	}
}

// convertFromGQLFileFilter converts GraphQL FileFilter to service FileFilter
func (s *FileService) convertFromGQLFileFilter(filter *gqlmodel.FileFilter) *FileFilter {
	if filter == nil {
		return nil
	}

	serviceFilter := &FileFilter{
		Tags:           filter.Tags,
		UploadedAfter:  filter.UploadedAfter,
		UploadedBefore: filter.UploadedBefore,
	}

	if filter.FileType != nil {
		fileType := string(*filter.FileType)
		serviceFilter.FileType = &fileType
	}

	if filter.Status != nil {
		status := string(*filter.Status)
		serviceFilter.Status = &status
	}

	return serviceFilter
}

// convertFromGQLPagination converts GraphQL PaginationInput to service PaginationInput
func (s *FileService) convertFromGQLPagination(pagination *gqlmodel.PaginationInput) *PaginationInput {
	if pagination == nil {
		return &PaginationInput{
			Limit:  20,
			Offset: 0,
		}
	}

	limit := 20
	offset := 0

	if pagination.Limit != nil {
		limit = *pagination.Limit
	}
	if pagination.Offset != nil {
		offset = *pagination.Offset
	}

	return &PaginationInput{
		Limit:  limit,
		Offset: offset,
	}
}

// convertToGQLFileListResponse converts service FileListResponse to GraphQL FileListResponse
func (s *FileService) convertToGQLFileListResponse(response *FileListResponse) *gqlmodel.FileListResponse {
	gqlFiles := make([]*gqlmodel.FileMetadata, len(response.Files))
	for i, file := range response.Files {
		gqlFiles[i] = s.convertToGQLFileMetadata(file)
	}

	return &gqlmodel.FileListResponse{
		Files:           gqlFiles,
		TotalCount:      response.TotalCount,
		HasNextPage:     response.HasNextPage,
		HasPreviousPage: response.HasPreviousPage,
	}
}

// DirectUpload handles direct file upload with file content
func (s *FileService) DirectUpload(ctx context.Context, input *gqlmodel.UploadInput, fileContent io.Reader, userID *string) (*gqlmodel.UploadResponse, error) {
	// First create the file metadata
	response, err := s.UploadFile(ctx, input, userID)
	if err != nil || !response.Success {
		return response, err
	}

	// Upload file to R2
	err = s.r2Service.UploadFile(ctx, response.File.Filename, fileContent, input.MimeType)
	if err != nil {
		// Get the internal file to update status
		internalFile, getErr := s.fileRepo.GetByID(ctx, response.File.ID)
		if getErr == nil && internalFile != nil {
			internalFile.Status = "ERROR"
			s.fileRepo.Update(ctx, internalFile)
		}

		return &gqlmodel.UploadResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to upload file to storage: %v", err),
		}, nil
	}

	// Get the internal file to update status
	internalFile, err := s.fileRepo.GetByID(ctx, response.File.ID)
	if err != nil {
		return &gqlmodel.UploadResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get file for update: %v", err),
		}, nil
	}

	// Update status to ready
	internalFile.Status = "READY"
	if err := s.fileRepo.Update(ctx, internalFile); err != nil {
		return &gqlmodel.UploadResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update file status: %v", err),
		}, nil
	}

	// Update response with new status
	response.File = s.convertToGQLFileMetadata(internalFile)
	response.Message = "File uploaded successfully"
	return response, nil
}

// CompleteUpload marks a signed upload as complete
func (s *FileService) CompleteUpload(ctx context.Context, fileID string) (*gqlmodel.FileMetadata, error) {
	// Get file metadata
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}
	if file == nil {
		return nil, fmt.Errorf("file not found")
	}

	// Check if file exists in R2
	exists, err := s.r2Service.FileExists(ctx, file.Filename)
	if err != nil {
		return nil, fmt.Errorf("failed to check file existence: %w", err)
	}

	if exists {
		file.Status = "READY"
	} else {
		file.Status = "ERROR"
	}

	// Update status
	if err := s.fileRepo.Update(ctx, file); err != nil {
		return nil, fmt.Errorf("failed to update file status: %w", err)
	}

	return s.convertToGQLFileMetadata(file), nil
}

// GetFile retrieves file metadata by ID
func (s *FileService) GetFile(ctx context.Context, fileID string) (*gqlmodel.FileMetadata, error) {
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, err
	}
	if file == nil {
		return nil, nil
	}
	return s.convertToGQLFileMetadata(file), nil
}

// ListFiles retrieves files with filtering and pagination
func (s *FileService) ListFiles(ctx context.Context, filter *gqlmodel.FileFilter, pagination *gqlmodel.PaginationInput) (*gqlmodel.FileListResponse, error) {
	// Convert GraphQL types to service types
	serviceFilter := s.convertFromGQLFileFilter(filter)
	servicePagination := s.convertFromGQLPagination(pagination)

	// Validate pagination
	if servicePagination.Limit <= 0 || servicePagination.Limit > 100 {
		servicePagination.Limit = 20
	}
	if servicePagination.Offset < 0 {
		servicePagination.Offset = 0
	}

	// Get results from repository
	result, err := s.fileRepo.List(ctx, serviceFilter, servicePagination)
	if err != nil {
		return nil, err
	}

	// Convert back to GraphQL types
	return s.convertToGQLFileListResponse(result), nil
}

// GenerateDownloadURL generates download URL (public or signed)
func (s *FileService) GenerateDownloadURL(ctx context.Context, fileID string, urlType gqlmodel.URLType, expiresIn *int) (*gqlmodel.DownloadResponse, error) {
	// Get file metadata
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return &gqlmodel.DownloadResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get file: %v", err),
		}, nil
	}
	if file == nil {
		return &gqlmodel.DownloadResponse{
			Success: false,
			Message: "File not found",
		}, nil
	}

	if file.Status != "READY" {
		return &gqlmodel.DownloadResponse{
			Success: false,
			Message: "File is not ready for download",
		}, nil
	}

	var url string
	var expiresAt *string

	switch urlType {
	case gqlmodel.URLTypePublic:
		// Use CDN URL if available, otherwise public URL
		if file.CDNURL != nil {
			url = *file.CDNURL
		} else if file.PublicURL != nil {
			url = *file.PublicURL
		} else {
			url = s.r2Service.GetPublicURL(file.Filename)
		}
	case gqlmodel.URLTypeSigned:
		expiry := time.Hour // Default 1 hour
		if expiresIn != nil && *expiresIn > 0 {
			expiry = time.Duration(*expiresIn) * time.Second
		}

		signedURL, err := s.r2Service.GeneratePresignedDownloadURL(ctx, file.Filename, expiry)
		if err != nil {
			return &gqlmodel.DownloadResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to generate signed URL: %v", err),
			}, nil
		}
		url = signedURL

		expiresAtTime := time.Now().Add(expiry).Format(time.RFC3339)
		expiresAt = &expiresAtTime
	default:
		return &gqlmodel.DownloadResponse{
			Success: false,
			Message: "Invalid URL type",
		}, nil
	}

	return &gqlmodel.DownloadResponse{
		Success:   true,
		Message:   "Download URL generated successfully",
		URL:       &url,
		ExpiresAt: expiresAt,
	}, nil
}

// DeleteFile deletes a file and its metadata
func (s *FileService) DeleteFile(ctx context.Context, fileID string) (bool, error) {
	// Get file metadata
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return false, fmt.Errorf("failed to get file: %w", err)
	}
	if file == nil {
		return false, fmt.Errorf("file not found")
	}

	// Delete from R2 (ignore errors if file doesn't exist)
	s.r2Service.DeleteFile(ctx, file.Filename)

	// Purge CDN cache if CDN service is available
	if s.cdnService != nil {
		urls := []string{}
		if file.PublicURL != nil {
			urls = append(urls, *file.PublicURL)
		}
		if file.CDNURL != nil {
			urls = append(urls, *file.CDNURL)
		}
		if len(urls) > 0 {
			// Ignore CDN purge errors, don't fail the delete operation
			s.cdnService.PurgeCache(ctx, urls)
		}
	}

	// Delete from database
	if err := s.fileRepo.Delete(ctx, fileID); err != nil {
		return false, fmt.Errorf("failed to delete file metadata: %w", err)
	}

	return true, nil
}

// UpdateFileMetadata updates file tags and metadata
func (s *FileService) UpdateFileMetadata(ctx context.Context, fileID string, tags []string, metadata *string) (*gqlmodel.FileMetadata, error) {
	// Get file metadata
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}
	if file == nil {
		return nil, fmt.Errorf("file not found")
	}

	// Update fields
	if tags != nil {
		file.Tags = tags
	}
	if metadata != nil {
		file.Metadata = metadata
	}

	// Save changes
	if err := s.fileRepo.Update(ctx, file); err != nil {
		return nil, fmt.Errorf("failed to update file metadata: %w", err)
	}

	return s.convertToGQLFileMetadata(file), nil
}

// validateUploadInput validates upload input
func (s *FileService) validateUploadInput(input *gqlmodel.UploadInput) error {
	if input.Filename == "" {
		return fmt.Errorf("filename is required")
	}

	if !input.FileType.IsValid() {
		return fmt.Errorf("invalid file type")
	}

	if input.MimeType == "" {
		return fmt.Errorf("mime type is required")
	}

	if input.Size <= 0 {
		return fmt.Errorf("file size must be greater than 0")
	}

	if int64(input.Size) > s.config.Upload.MaxFileSize {
		return fmt.Errorf("file size exceeds maximum allowed size")
	}

	// Validate file extension
	ext := strings.ToLower(filepath.Ext(input.Filename))
	if ext != "" {
		allowed := false
		for _, allowedExt := range s.config.Upload.AllowedExtensions {
			// Normalize allowed extension (remove dot if present)
			normalizedAllowedExt := strings.ToLower(allowedExt)
			if strings.HasPrefix(normalizedAllowedExt, ".") {
				normalizedAllowedExt = normalizedAllowedExt[1:]
			}

			// Compare with file extension (without dot)
			fileExt := ext[1:] // Remove the dot from file extension
			if fileExt == normalizedAllowedExt {
				allowed = true
				break
			}
		}
		if !allowed {
			return fmt.Errorf("file extension not allowed")
		}
	}

	return nil
}

// GetOptimizedImageURL returns an optimized image URL with transformation options
func (s *FileService) GetOptimizedImageURL(ctx context.Context, fileID string, options ImageOptimizationOptions) (*string, error) {
	// Get file metadata
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}
	if file == nil {
		return nil, fmt.Errorf("file not found")
	}

	// Only optimize images
	if file.FileType != "IMAGE" {
		return nil, fmt.Errorf("file is not an image")
	}

	if file.Status != "READY" {
		return nil, fmt.Errorf("file is not ready")
	}

	// Use CDN service for optimization if available
	if s.cdnService != nil {
		baseURL := ""
		if file.CDNURL != nil {
			baseURL = *file.CDNURL
		} else if file.PublicURL != nil {
			baseURL = *file.PublicURL
		} else {
			baseURL = s.r2Service.GetPublicURL(file.Filename)
		}

		optimizedURL := s.cdnService.GetOptimizedImageURL(baseURL, options)
		return &optimizedURL, nil
	}

	// Fallback to regular URL if CDN service not available
	if file.CDNURL != nil {
		return file.CDNURL, nil
	} else if file.PublicURL != nil {
		return file.PublicURL, nil
	}

	publicURL := s.r2Service.GetPublicURL(file.Filename)
	return &publicURL, nil
}

// getExtensionFromMimeType returns file extension based on mime type
func (s *FileService) getExtensionFromMimeType(mimeType string) string {
	switch mimeType {
	case "image/jpeg":
		return ".jpg"
	case "image/png":
		return ".png"
	case "image/gif":
		return ".gif"
	case "image/webp":
		return ".webp"
	case "image/svg+xml":
		return ".svg"
	case "video/mp4":
		return ".mp4"
	case "video/quicktime":
		return ".mov"
	case "video/x-msvideo":
		return ".avi"
	case "video/webm":
		return ".webm"
	default:
		return ""
	}
}
