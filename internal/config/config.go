package config

import (
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/spf13/viper"
)

type Config struct {
	System   SystemConfig   `mapstructure:"system"`
	Database DatabaseConfig `mapstructure:"pgsql"`
	R2       R2Config       `mapstructure:"r2"`
	CDN      CDNConfig      `mapstructure:"cdn"`
	Upload   UploadConfig   `mapstructure:"upload"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	Zap      ZapConfig      `mapstructure:"zap"`
}

var GlobalConfig *Config

func InitConfig() {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./config")
	viper.AddConfigPath(".")

	// Set default values
	setDefaults()

	// Read environment variables
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_", "-", "_"))
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		log.Printf("Error reading config file: %v", err)
	}

	if err := viper.Unmarshal(&GlobalConfig); err != nil {
		log.Fatalf("Unable to decode config into struct: %v", err)
	}
}

func setDefaults() {
	// System defaults
	viper.SetDefault("system.env", "local")
	viper.SetDefault("system.addr", "8080")
	viper.SetDefault("system.router-prefix", "/api/cdn")
	viper.SetDefault("system.graphql-prefix", "/api/cdn/graphql")

	// Database defaults
	viper.SetDefault("pgsql.path", "127.0.0.1")
	viper.SetDefault("pgsql.port", "5433")
	viper.SetDefault("pgsql.config", "sslmode=disable")
	viper.SetDefault("pgsql.db-name", "xbit_cdn")
	viper.SetDefault("pgsql.username", "postgres")
	viper.SetDefault("pgsql.password", "postgres")
	viper.SetDefault("pgsql.prefix", "")
	viper.SetDefault("pgsql.singular", false)
	viper.SetDefault("pgsql.engine", "")
	viper.SetDefault("pgsql.max-idle-conns", 10)
	viper.SetDefault("pgsql.max-open-conns", 100)
	viper.SetDefault("pgsql.log-mode", "")
	viper.SetDefault("pgsql.log-zap", false)

	// R2 defaults
	viper.SetDefault("r2.region", "auto")

	// CDN defaults
	viper.SetDefault("cdn.enabled", false)
	viper.SetDefault("cdn.provider", "cloudflare")
	viper.SetDefault("cdn.cache-ttl", 3600)
	viper.SetDefault("cdn.image-optimize", true)

	// Upload defaults
	viper.SetDefault("upload.max-file-size", 104857600)
	viper.SetDefault("upload.allowed-extensions", []string{".jpg", ".jpeg", ".png", ".gif", ".webp", ".pdf", ".mp4"})
	viper.SetDefault("upload.signed-url-expiry", 3600)
	viper.SetDefault("upload.chunk-size", 5242880)

	// JWT defaults
	viper.SetDefault("jwt.signing-key", "xbit-cdn-service")
	viper.SetDefault("jwt.expires-time", "7d")
	viper.SetDefault("jwt.buffer-time", "1d")
	viper.SetDefault("jwt.issuer", "xbit-cdn-service")

	// Zap defaults
	viper.SetDefault("zap.level", "info")
	viper.SetDefault("zap.format", "console")
	viper.SetDefault("zap.prefix", "[xbit-cdn-service]")
	viper.SetDefault("zap.director", "log")
	viper.SetDefault("zap.encode-level", "LowercaseColorLevelEncoder")
	viper.SetDefault("zap.stacktrace-key", "stacktrace")
	viper.SetDefault("zap.max-age", 0)
	viper.SetDefault("zap.show-line", true)
	viper.SetDefault("zap.log-in-console", true)
}

type SystemConfig struct {
	Env           string `mapstructure:"env"`
	Addr          string `mapstructure:"addr"`
	RouterPrefix  string `mapstructure:"router-prefix"`
	GraphQLPrefix string `mapstructure:"graphql-prefix"`
}

type DatabaseConfig struct {
	Path         string `mapstructure:"path"`
	Port         string `mapstructure:"port"`
	Config       string `mapstructure:"config"`
	DbName       string `mapstructure:"db-name"`
	Username     string `mapstructure:"username"`
	Password     string `mapstructure:"password"`
	Prefix       string `mapstructure:"prefix"`
	Singular     bool   `mapstructure:"singular"`
	Engine       string `mapstructure:"engine"`
	MaxIdleConns int    `mapstructure:"max-idle-conns"`
	MaxOpenConns int    `mapstructure:"max-open-conns"`
	LogMode      string `mapstructure:"log-mode"`
	LogZap       bool   `mapstructure:"log-zap"`
}

func (d *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s %s",
		d.Path, d.Port, d.Username, d.Password, d.DbName, d.Config)
}

func (d *DatabaseConfig) GetPort() int {
	port, err := strconv.Atoi(d.Port)
	if err != nil {
		return 5432
	}
	return port
}

func (d *DatabaseConfig) GetMaxIdleConns() int {
	if d.MaxIdleConns <= 0 {
		return 10
	}
	return d.MaxIdleConns
}

func (d *DatabaseConfig) GetMaxOpenConns() int {
	if d.MaxOpenConns <= 0 {
		return 100
	}
	return d.MaxOpenConns
}

type R2Config struct {
	AccountID       string `mapstructure:"account-id"`
	AccessKeyID     string `mapstructure:"access-key-id"`
	SecretAccessKey string `mapstructure:"secret-access-key"`
	BucketName      string `mapstructure:"bucket-name"`
	Region          string `mapstructure:"region"`
	Endpoint        string `mapstructure:"endpoint"`
}

type JWTConfig struct {
	SigningKey  string `mapstructure:"signing-key"`
	ExpiresTime string `mapstructure:"expires-time"`
	BufferTime  string `mapstructure:"buffer-time"`
	Issuer      string `mapstructure:"issuer"`
}

type ZapConfig struct {
	Level         string `mapstructure:"level"`
	Format        string `mapstructure:"format"`
	Prefix        string `mapstructure:"prefix"`
	Director      string `mapstructure:"director"`
	EncodeLevel   string `mapstructure:"encode-level"`
	StacktraceKey string `mapstructure:"stacktrace-key"`
	MaxAge        int    `mapstructure:"max-age"`
	ShowLine      bool   `mapstructure:"show-line"`
	LogInConsole  bool   `mapstructure:"log-in-console"`
}

// CDNConfig represents CDN configuration
type CDNConfig struct {
	Enabled       bool   `mapstructure:"enabled"`
	Provider      string `mapstructure:"provider"`
	BaseURL       string `mapstructure:"base-url"`
	APIToken      string `mapstructure:"api-token"`
	ZoneID        string `mapstructure:"zone-id"`
	APIEmail      string `mapstructure:"api-email"`
	Domain        string `mapstructure:"domain"`
	CacheTTL      int    `mapstructure:"cache-ttl"`
	ImageOptimize bool   `mapstructure:"image-optimize"`
}

// UploadConfig represents file upload configuration
type UploadConfig struct {
	MaxFileSize       int64    `mapstructure:"max-file-size"`
	AllowedExtensions []string `mapstructure:"allowed-extensions"`
	SignedURLExpiry   int      `mapstructure:"signed-url-expiry"`
	ChunkSize         int64    `mapstructure:"chunk-size"`
}
