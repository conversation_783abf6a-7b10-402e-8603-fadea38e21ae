package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type File struct {
	ID           string    `gorm:"primaryKey;type:varchar(255)" json:"id"`
	Filename     string    `gorm:"not null;type:varchar(255)" json:"filename"`
	OriginalName string    `gorm:"not null;type:varchar(255)" json:"original_name"`
	FileType     string    `gorm:"not null;type:varchar(50)" json:"file_type"`
	MimeType     string    `gorm:"not null;type:varchar(100)" json:"mime_type"`
	Size         int64     `gorm:"not null" json:"size"`
	Status       string    `gorm:"not null;default:'UPLOADING';type:varchar(50)" json:"status"`
	UploadedAt   time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"uploaded_at"`
	UpdatedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	PublicURL    *string   `gorm:"type:text" json:"public_url"`
	CDNURL       *string   `gorm:"type:text" json:"cdn_url"`
	Tags         []string  `gorm:"type:text[]" json:"tags"`
	Metadata     *string   `gorm:"type:text" json:"metadata"`
	UserID       *string   `gorm:"type:varchar(255)" json:"user_id"`
}

func (f *File) BeforeCreate(tx *gorm.DB) error {
	if f.ID == "" {
		f.ID = uuid.New().String()
	}
	return nil
}
