package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

const (
	baseURL    = "http://127.0.0.1:8080"
	graphqlURL = baseURL + "/api/cdn-service/graphql"
	authURL    = baseURL + "/auth/login"
	healthURL  = baseURL + "/api/cdn-service/graphql/healthz"
)

type GraphQLRequest struct {
	Query     string                 `json:"query"`
	Variables map[string]interface{} `json:"variables,omitempty"`
}

type GraphQLResponse struct {
	Data   interface{} `json:"data"`
	Errors []struct {
		Message string        `json:"message"`
		Path    []interface{} `json:"path"`
	} `json:"errors"`
}

type AuthResponse struct {
	Token     string `json:"token"`
	ExpiresIn int    `json:"expires_in"`
	User      struct {
		ID       string   `json:"id"`
		Username string   `json:"username"`
		Email    string   `json:"email"`
		Roles    []string `json:"roles"`
	} `json:"user"`
}

func main() {
	fmt.Println("🚀 Testing XBIT CDN Service GraphQL API")
	fmt.Println(strings.Repeat("=", 50))

	// Test 1: Health Check
	fmt.Println("\n📋 Test 1: Health Check")
	testHealthCheck()

	// Test 2: GraphQL Health Query
	fmt.Println("\n📋 Test 2: GraphQL Health Query")
	testGraphQLHealth()

	// Test 3: Authentication
	fmt.Println("\n📋 Test 3: Authentication")
	token := testAuthentication()

	// Test 4: File Upload (Signed URL)
	fmt.Println("\n📋 Test 4: File Upload (Signed URL)")
	fileID := testFileUpload(token)

	// Test 5: Get File Info
	if fileID != "" {
		fmt.Println("\n📋 Test 5: Get File Info")
		testGetFile(token, fileID)

		// Test 6: Generate Download URL
		fmt.Println("\n📋 Test 6: Generate Download URL")
		testDownloadURL(token, fileID)

		// Test 7: List Files
		fmt.Println("\n📋 Test 7: List Files")
		testListFiles(token)
	}

	fmt.Println("\n✅ All tests completed!")
}

func testHealthCheck() {
	resp, err := http.Get(healthURL)
	if err != nil {
		fmt.Printf("❌ Health check failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("✅ Health check passed: %s\n", string(body))
}

func testGraphQLHealth() {
	query := `query { health }`

	response := makeGraphQLRequest(query, nil, "")
	if response.Errors != nil {
		fmt.Printf("❌ GraphQL health query failed: %v\n", response.Errors)
		return
	}

	fmt.Printf("✅ GraphQL health query passed: %v\n", response.Data)
}

func testAuthentication() string {
	authData := map[string]string{
		"username": "admin",
		"password": "admin123",
	}

	jsonData, _ := json.Marshal(authData)
	resp, err := http.Post(authURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Authentication failed: %v\n", err)
		return ""
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != 200 {
		fmt.Printf("❌ Authentication failed with status %d: %s\n", resp.StatusCode, string(body))
		return ""
	}

	var authResp AuthResponse
	if err := json.Unmarshal(body, &authResp); err != nil {
		fmt.Printf("❌ Failed to parse auth response: %v\n", err)
		return ""
	}

	fmt.Printf("✅ Authentication successful for user: %s\n", authResp.User.Username)
	return authResp.Token
}

func testFileUpload(token string) string {
	query := `
		mutation UploadFile($input: UploadInput!) {
			uploadFile(input: $input) {
				success
				message
				uploadURL
				file {
					id
					filename
					status
					fileType
					mimeType
					size
				}
			}
		}
	`

	variables := map[string]interface{}{
		"input": map[string]interface{}{
			"filename":        "test-image.jpg",
			"fileType":        "IMAGE",
			"mimeType":        "image/jpeg",
			"size":            1024000,
			"tags":            []string{"test", "demo"},
			"useSignedUpload": true,
		},
	}

	response := makeGraphQLRequest(query, variables, token)
	if response.Errors != nil {
		fmt.Printf("❌ File upload failed: %v\n", response.Errors)
		return ""
	}

	// Parse response to get file ID
	data, _ := json.Marshal(response.Data)
	var uploadResp struct {
		UploadFile struct {
			Success   bool   `json:"success"`
			Message   string `json:"message"`
			UploadURL string `json:"uploadURL"`
			File      struct {
				ID       string `json:"id"`
				Filename string `json:"filename"`
				Status   string `json:"status"`
			} `json:"file"`
		} `json:"uploadFile"`
	}

	json.Unmarshal(data, &uploadResp)

	if uploadResp.UploadFile.Success {
		fmt.Printf("✅ File upload successful: %s (ID: %s)\n",
			uploadResp.UploadFile.File.Filename,
			uploadResp.UploadFile.File.ID)
		fmt.Printf("   Upload URL: %s\n", uploadResp.UploadFile.UploadURL)
		return uploadResp.UploadFile.File.ID
	} else {
		fmt.Printf("❌ File upload failed: %s\n", uploadResp.UploadFile.Message)
		return ""
	}
}

func testGetFile(token, fileID string) {
	query := `
		query GetFile($id: ID!) {
			file(id: $id) {
				id
				filename
				originalName
				fileType
				mimeType
				size
				status
				uploadedAt
				updatedAt
				publicURL
				cdnURL
				tags
			}
		}
	`

	variables := map[string]interface{}{
		"id": fileID,
	}

	response := makeGraphQLRequest(query, variables, token)
	if response.Errors != nil {
		fmt.Printf("❌ Get file failed: %v\n", response.Errors)
		return
	}

	fmt.Printf("✅ Get file successful: %v\n", response.Data)
}

func testDownloadURL(token, fileID string) {
	query := `
		query GenerateDownloadURL($id: ID!, $urlType: URLType, $expiresIn: Int) {
			downloadURL(id: $id, urlType: $urlType, expiresIn: $expiresIn) {
				success
				message
				url
				expiresAt
			}
		}
	`

	variables := map[string]interface{}{
		"id":        fileID,
		"urlType":   "PUBLIC",
		"expiresIn": 3600,
	}

	response := makeGraphQLRequest(query, variables, token)
	if response.Errors != nil {
		fmt.Printf("❌ Generate download URL failed: %v\n", response.Errors)
		return
	}

	fmt.Printf("✅ Generate download URL successful: %v\n", response.Data)
}

func testListFiles(token string) {
	query := `
		query ListFiles($filter: FileFilter, $pagination: PaginationInput) {
			files(filter: $filter, pagination: $pagination) {
				files {
					id
					filename
					fileType
					status
					uploadedAt
				}
				totalCount
				hasNextPage
				hasPreviousPage
			}
		}
	`

	variables := map[string]interface{}{
		"pagination": map[string]interface{}{
			"limit":  10,
			"offset": 0,
		},
	}

	response := makeGraphQLRequest(query, variables, token)
	if response.Errors != nil {
		fmt.Printf("❌ List files failed: %v\n", response.Errors)
		return
	}

	fmt.Printf("✅ List files successful: %v\n", response.Data)
}

func makeGraphQLRequest(query string, variables map[string]interface{}, token string) GraphQLResponse {
	reqBody := GraphQLRequest{
		Query:     query,
		Variables: variables,
	}

	jsonData, _ := json.Marshal(reqBody)
	req, _ := http.NewRequest("POST", graphqlURL, bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return GraphQLResponse{Errors: []struct {
			Message string        `json:"message"`
			Path    []interface{} `json:"path"`
		}{{Message: err.Error()}}}
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	var response GraphQLResponse
	json.Unmarshal(body, &response)

	return response
}
