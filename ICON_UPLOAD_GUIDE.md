# 🎨 Icon Upload Guide

Hướng dẫn upload các icon SVG lên XBIT CDN Service.

## 📋 Danh sách Icon có sẵn

Hiện tại có **9 icon SVG** trong thư mục `images/icons`:

| Icon | File | Size | Mô tả |
|------|------|------|-------|
| 📊 | `all-trade-tasks.svg` | 10.5 KB | Tất cả nhiệm vụ giao dịch |
| 📅 | `calendar.svg` | 4.8 KB | Lịch |
| 🐦 | `follow-x.svg` | 4.7 KB | Theo dõi X (Twitter) |
| ❤️ | `like-tweet.svg` | 3.6 KB | Thích tweet |
| 📈 | `make-one-contract-trade.svg` | 4.5 KB | Giao dịch hợp đồng |
| 🎭 | `make-one-meme-trade.svg` | 5.2 KB | Giao dịch meme |
| 🔄 | `retweet.svg` | 3.7 KB | Retweet |
| 📊 | `share-earning-chart.svg` | 4.7 KB | Chia sẻ biểu đồ thu nhập |
| 👁️ | `view-market-page.svg` | 4.1 KB | Xem trang thị trường |

## 🚀 Các lệnh Upload

### 1. Xem danh sách icon
```bash
make list-icons
```

### 2. Upload tất cả icon
```bash
make upload-icons
```

### 3. Upload và xem preview
```bash
make upload-icons-preview
```

### 4. Test upload một icon
```bash
# Upload icon mặc định (all-trade-tasks.svg)
make test-upload-icon

# Upload icon cụ thể
make test-upload-icon ICON_NAME=calendar.svg
```

### 5. Dọn dẹp kết quả upload
```bash
make clean-icons
```

## 📊 Kết quả Upload

Sau khi upload thành công, bạn sẽ nhận được:

### 1. File JSON kết quả (`upload-results.json`)
```json
[
  {
    "success": true,
    "message": "File uploaded successfully",
    "file": {
      "id": "uuid-file-id",
      "filename": "uuid-filename.svg",
      "originalName": "all-trade-tasks.svg",
      "fileType": "IMAGE",
      "mimeType": "image/svg+xml",
      "size": 10468,
      "status": "READY",
      "uploadedAt": "2024-01-01T00:00:00Z",
      "publicURL": "https://pub-account-id.r2.dev/uuid-filename.svg",
      "cdnURL": "https://your-cdn-domain.com/uuid-filename.svg",
      "tags": ["icon", "svg", "all-trade-tasks"]
    }
  }
]
```

### 2. HTML Preview (`icon-upload-preview.html`)
- Xem trước tất cả icon đã upload
- Hiển thị URL và thông tin chi tiết
- Thống kê upload

## 🔗 Sử dụng Icon sau khi Upload

### Trong HTML
```html
<img src="https://pub-account-id.r2.dev/uuid-filename.svg" alt="All Trade Tasks" />
```

### Trong CSS
```css
.icon {
  background-image: url('https://pub-account-id.r2.dev/uuid-filename.svg');
  background-size: contain;
  background-repeat: no-repeat;
}
```

### Trong React/JavaScript
```jsx
const IconComponent = () => (
  <img
    src="https://pub-account-id.r2.dev/uuid-filename.svg"
    alt="All Trade Tasks"
    width="24"
    height="24"
  />
);
```

## ⚙️ Cấu hình

### Yêu cầu
- CDN Service phải đang chạy (`make run-local`)
- File SVG phải có trong `images/icons/`
- Đã cấu hình R2 credentials

### Tags tự động
Mỗi icon sẽ được gắn tags:
- `icon` - Loại file
- `svg` - Format
- `[tên-file]` - Tên file (không có extension)

### Metadata
```json
{
  "type": "icon",
  "format": "svg",
  "category": "ui"
}
```

## 🛠️ Troubleshooting

### Lỗi "Service not running"
```bash
# Khởi động service
make run-local

# Kiểm tra health
curl http://localhost:8080/api/cdn-service/graphql/healthz
```

### Lỗi "Authentication failed"
- Kiểm tra username/password trong code
- Mặc định: `admin` / `admin123`

### Lỗi "File not found"
```bash
# Kiểm tra file có tồn tại
ls -la images/icons/*.svg

# Kiểm tra quyền truy cập
chmod +x scripts/upload-icons.sh
```

### Lỗi "Upload failed"
- Kiểm tra R2 credentials trong config
- Kiểm tra kết nối mạng
- Xem log chi tiết trong response

## 📝 Ví dụ sử dụng

### Upload tất cả icon và tạo preview
```bash
# 1. Khởi động service
make run-local

# 2. Upload tất cả icon
make upload-icons

# 3. Xem preview
open icon-upload-preview.html
```

### Upload một icon cụ thể
```bash
# Upload icon calendar
make test-upload-icon ICON_NAME=calendar.svg

# Kết quả sẽ hiển thị URL để sử dụng
```

### Kiểm tra icon đã upload
```bash
# Xem danh sách icon local
make list-icons

# Kiểm tra kết quả upload
cat upload-results.json | jq '.[].file.originalName'
```

## 🎯 Best Practices

1. **Luôn test trước khi upload hàng loạt**
   ```bash
   make test-upload-icon ICON_NAME=calendar.svg
   ```

2. **Kiểm tra kết quả upload**
   - Xem file `upload-results.json`
   - Mở `icon-upload-preview.html`

3. **Lưu trữ URLs**
   - Copy URLs từ kết quả upload
   - Lưu vào config hoặc database

4. **Sử dụng CDN URLs**
   - Ưu tiên `cdnURL` nếu có
   - Fallback về `publicURL`

5. **Clean up định kỳ**
   ```bash
   make clean-icons
   ```

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra log của service
2. Xem file `upload-results.json` để debug
3. Test với một icon đơn lẻ trước
4. Kiểm tra cấu hình R2

---

**Happy Uploading! 🚀**
