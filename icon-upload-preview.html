<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XBIT CDN Icons Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .icon-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #fafafa;
            transition: transform 0.2s;
        }
        .icon-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .icon-preview {
            width: 64px;
            height: 64px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .icon-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .icon-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .icon-url {
            font-size: 12px;
            color: #666;
            word-break: break-all;
            margin-bottom: 5px;
        }
        .icon-id {
            font-size: 11px;
            color: #999;
            font-family: monospace;
        }
        .stats {
            background: #e8f4fd;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .stats h3 {
            margin: 0 0 10px 0;
            color: #0066cc;
        }
        .stats p {
            margin: 5px 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 XBIT CDN Icons Preview</h1>

        <div class="stats">
            <h3>📊 Upload Statistics</h3>
            <p id="stats-content">Loading...</p>
        </div>

        <div class="icon-grid" id="icon-grid">
            <!-- Icons will be loaded here -->
        </div>
    </div>

    <script>
        // Load and display icons
        fetch('upload-results.json')
            .then(response => response.json())
            .then(data => {
                const iconGrid = document.getElementById('icon-grid');
                const statsContent = document.getElementById('stats-content');

                if (Array.isArray(data) && data.length > 0) {
                    // Display stats
                    const successCount = data.filter(item => item.success).length;
                    const totalCount = data.length;
                    statsContent.innerHTML = `
                        <p><strong>Total Icons:</strong> ${totalCount}</p>
                        <p><strong>Successfully Uploaded:</strong> ${successCount}</p>
                        <p><strong>Success Rate:</strong> ${Math.round((successCount/totalCount)*100)}%</p>
                    `;

                    // Display icons
                    data.forEach(item => {
                        if (item.success && item.file) {
                            const iconItem = document.createElement('div');
                            iconItem.className = 'icon-item';
                            iconItem.innerHTML = `
                                <div class="icon-preview">
                                    <img src="${item.file.publicURL}" alt="${item.file.originalName}" onerror="this.style.display='none'">
                                </div>
                                <div class="icon-name">${item.file.originalName}</div>
                                <div class="icon-url">${item.file.publicURL}</div>
                                <div class="icon-id">ID: ${item.file.id}</div>
                            `;
                            iconGrid.appendChild(iconItem);
                        }
                    });
                } else {
                    statsContent.innerHTML = '<p>No upload results found</p>';
                }
            })
            .catch(error => {
                console.error('Error loading results:', error);
                document.getElementById('stats-content').innerHTML = '<p>Error loading results</p>';
            });
    </script>
</body>
</html>
