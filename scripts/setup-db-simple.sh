#!/bin/bash

# Simple database setup script
# This script creates the necessary database and table

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}🚀 Setting up database for XBIT CDN Service${NC}"

# Check if we can connect to PostgreSQL
echo "Checking PostgreSQL connection..."

# Try to create database using Docker if available
if command -v docker >/dev/null 2>&1; then
    echo "Using Docker to setup database..."

    # Start PostgreSQL container
    echo "Starting PostgreSQL container..."
    docker run --name xbit-cdn-postgres -e POSTGRES_PASSWORD=postgres -e POSTGRES_DB=xbit_cdn -p 5433:5432 -d postgres:15 || {
        echo "Container might already exist, trying to start it..."
        docker start xbit-cdn-postgres || true
    }

    # Wait for PostgreSQL to be ready
    echo "Waiting for PostgreSQL to be ready..."
    sleep 5

    # Create table
    echo "Creating files table..."
    docker exec xbit-cdn-postgres psql -U postgres -d xbit_cdn -c "
        CREATE TABLE IF NOT EXISTS files (
            id VARCHAR(255) PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            original_name VARCHAR(255) NOT NULL,
            file_type VARCHAR(50) NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            size BIGINT NOT NULL,
            status VARCHAR(50) NOT NULL DEFAULT 'UPLOADING',
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            public_url TEXT,
            cdn_url TEXT,
            tags TEXT[],
            metadata TEXT,
            user_id VARCHAR(255)
        );

        CREATE INDEX IF NOT EXISTS idx_files_user_id ON files(user_id);
        CREATE INDEX IF NOT EXISTS idx_files_status ON files(status);
        CREATE INDEX IF NOT EXISTS idx_files_file_type ON files(file_type);
        CREATE INDEX IF NOT EXISTS idx_files_uploaded_at ON files(uploaded_at);
    "

    echo -e "${GREEN}✅ Database setup completed!${NC}"
    echo "PostgreSQL is running on port 5433"
    echo "Database: xbit_cdn"
    echo "Username: postgres"
    echo "Password: postgres"

else
    echo -e "${RED}❌ Docker not found. Please install Docker or setup PostgreSQL manually${NC}"
    echo "You can also use: brew install postgresql"
    exit 1
fi
