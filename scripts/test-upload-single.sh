#!/bin/bash

# Test script to upload a single icon
# Usage: ./scripts/test-upload-single.sh [icon-name]

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Configuration
BASE_URL="http://localhost:8080"
UPLOAD_URL="$BASE_URL/api/cdn-service/upload"
AUTH_URL="$BASE_URL/auth/login"

# Get icon name from argument or use default
ICON_NAME="${1:-all-trade-tasks.svg}"
ICON_PATH="images/icons/$ICON_NAME"

echo -e "${YELLOW}🚀 Testing upload of $ICON_NAME${NC}"

# Check if file exists
if [ ! -f "$ICON_PATH" ]; then
    echo -e "${RED}❌ File $ICON_PATH not found${NC}"
    exit 1
fi

# Check if service is running
echo "Checking if service is running..."
if ! curl -s -f "$BASE_URL/api/cdn-service/graphql/healthz" > /dev/null 2>&1; then
    echo -e "${RED}❌ CDN service is not running${NC}"
    echo "Please start with: make run-local"
    exit 1
fi

# Get auth token
echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST "$AUTH_URL" \
    -H "Content-Type: application/json" \
    -d '{
        "username": "admin",
        "password": "admin123"
    }')

TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.token // empty' 2>/dev/null)

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
    echo -e "${RED}❌ Failed to get authentication token${NC}"
    echo "Response: $AUTH_RESPONSE"
    exit 1
fi

echo -e "${GREEN}✅ Authentication successful${NC}"

# Upload the file
echo "Uploading $ICON_NAME..."
RESPONSE=$(curl -s -X POST "$UPLOAD_URL" \
    -H "Authorization: Bearer $TOKEN" \
    -F "file=@$ICON_PATH" \
    -F "fileType=IMAGE" \
    -F "tags=icon,svg,test" \
    -F "metadata={\"type\": \"icon\", \"format\": \"svg\", \"test\": true}")

# Check response
SUCCESS=$(echo "$RESPONSE" | jq -r '.success // false' 2>/dev/null)

if [ "$SUCCESS" = "true" ]; then
    FILE_ID=$(echo "$RESPONSE" | jq -r '.file.id // empty' 2>/dev/null)
    PUBLIC_URL=$(echo "$RESPONSE" | jq -r '.file.publicURL // empty' 2>/dev/null)
    CDN_URL=$(echo "$RESPONSE" | jq -r '.file.cdnURL // empty' 2>/dev/null)

    echo -e "${GREEN}✅ Upload successful!${NC}"
    echo "File ID: $FILE_ID"
    echo "Public URL: $PUBLIC_URL"
    if [ -n "$CDN_URL" ] && [ "$CDN_URL" != "null" ]; then
        echo "CDN URL: $CDN_URL"
    fi

    # Test the URL
    echo ""
    echo "Testing the uploaded URL..."
    if curl -s -f "$PUBLIC_URL" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ URL is accessible!${NC}"
        echo "You can view the icon at: $PUBLIC_URL"
    else
        echo -e "${YELLOW}⚠️  URL might not be accessible yet (R2 propagation delay)${NC}"
    fi
else
    MESSAGE=$(echo "$RESPONSE" | jq -r '.message // "Unknown error"' 2>/dev/null)
    echo -e "${RED}❌ Upload failed: $MESSAGE${NC}"
    echo "Response: $RESPONSE"
    exit 1
fi
