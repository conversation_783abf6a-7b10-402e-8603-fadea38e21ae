#!/bin/bash

# XBIT CDN Service - Upload Icons <PERSON>ript
# Uploads all SVG icons from images/icons directory to the CDN service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:8080"
UPLOAD_URL="$BASE_URL/api/cdn-service/upload"
AUTH_URL="$BASE_URL/auth/login"
ICONS_DIR="images/icons"
RESULTS_FILE="upload-results.json"

# Function to print colored output
print_step() {
    echo -e "\n${BLUE}🚀 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if service is running
check_service() {
    print_step "Checking if CDN service is running..."

    if curl -s -f "$BASE_URL/api/cdn-service/graphql/healthz" > /dev/null 2>&1; then
        print_success "CDN service is running"
        return 0
    else
        print_error "CDN service is not running or not accessible at $BASE_URL"
        print_info "Please start the service with: make run-local"
        return 1
    fi
}

# Function to get authentication token
get_auth_token() {
    print_step "Getting authentication token..." >&2

    AUTH_RESPONSE=$(curl -s -X POST "$AUTH_URL" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "admin",
            "password": "admin123"
        }' 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "Failed to connect to authentication service" >&2
        return 1
    fi

    TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.token // empty' 2>/dev/null)

    if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
        print_error "Failed to get authentication token" >&2
        print_info "Response: $AUTH_RESPONSE" >&2
        return 1
    fi

    print_success "Authentication successful" >&2
    echo "$TOKEN"
}

# Function to upload a single icon
upload_icon() {
    local file_path="$1"
    local filename=$(basename "$file_path")
    local token="$2"

    print_info "Uploading $filename..."

    # Get file size
    local file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null)

    # Upload the file
    local response=$(curl -s -X POST "$UPLOAD_URL" \
        -H "Authorization: Bearer $token" \
        -F "file=@$file_path" \
        -F "fileType=IMAGE" \
        -F "tags=icon,svg,$(basename "$filename" .svg)" \
        -F "metadata={\"type\": \"icon\", \"format\": \"svg\", \"category\": \"ui\"}")

    if [ $? -ne 0 ]; then
        print_error "Failed to upload $filename"
        return 1
    fi

    # Check if upload was successful
    local success=$(echo "$response" | jq -r '.success // false' 2>/dev/null)

    if [ "$success" = "true" ]; then
        local file_id=$(echo "$response" | jq -r '.file.id // empty' 2>/dev/null)
        local public_url=$(echo "$response" | jq -r '.file.publicURL // empty' 2>/dev/null)
        local cdn_url=$(echo "$response" | jq -r '.file.cdnURL // empty' 2>/dev/null)

        print_success "Uploaded $filename successfully"
        print_info "  File ID: $file_id"
        print_info "  Public URL: $public_url"
        if [ -n "$cdn_url" ] && [ "$cdn_url" != "null" ]; then
            print_info "  CDN URL: $cdn_url"
        fi

        # Add to results
        echo "$response" >> "$RESULTS_FILE"

        return 0
    else
        local message=$(echo "$response" | jq -r '.message // "Unknown error"' 2>/dev/null)
        print_error "Failed to upload $filename: $message"
        return 1
    fi
}

# Function to upload all icons
upload_all_icons() {
    local token="$1"
    local success_count=0
    local total_count=0

    print_step "Starting upload of all icons..."

    # Initialize results file
    echo "[]" > "$RESULTS_FILE"

    # Find all SVG files in icons directory
    if [ ! -d "$ICONS_DIR" ]; then
        print_error "Icons directory '$ICONS_DIR' not found"
        return 1
    fi

    # Get list of SVG files
    local svg_files=($(find "$ICONS_DIR" -name "*.svg" -type f))

    if [ ${#svg_files[@]} -eq 0 ]; then
        print_warning "No SVG files found in $ICONS_DIR"
        return 0
    fi

    total_count=${#svg_files[@]}
    print_info "Found $total_count SVG files to upload"

    # Upload each file
    for file_path in "${svg_files[@]}"; do
        if upload_icon "$file_path" "$token"; then
            ((success_count++))
        fi
        echo "" # Add blank line for readability
    done

    # Summary
    print_step "Upload Summary"
    print_info "Total files: $total_count"
    print_success "Successfully uploaded: $success_count"
    if [ $success_count -lt $total_count ]; then
        print_error "Failed uploads: $((total_count - success_count))"
    fi

    # Show results file location
    if [ -f "$RESULTS_FILE" ]; then
        print_info "Detailed results saved to: $RESULTS_FILE"
    fi

    return $((total_count - success_count))
}

# Function to generate HTML preview
generate_html_preview() {
    local results_file="$1"

    if [ ! -f "$results_file" ]; then
        print_warning "No results file found to generate HTML preview"
        return 1
    fi

    local html_file="icon-upload-preview.html"

    print_step "Generating HTML preview..."

    cat > "$html_file" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XBIT CDN Icons Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .icon-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #fafafa;
            transition: transform 0.2s;
        }
        .icon-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .icon-preview {
            width: 64px;
            height: 64px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .icon-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .icon-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .icon-url {
            font-size: 12px;
            color: #666;
            word-break: break-all;
            margin-bottom: 5px;
        }
        .icon-id {
            font-size: 11px;
            color: #999;
            font-family: monospace;
        }
        .stats {
            background: #e8f4fd;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .stats h3 {
            margin: 0 0 10px 0;
            color: #0066cc;
        }
        .stats p {
            margin: 5px 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 XBIT CDN Icons Preview</h1>

        <div class="stats">
            <h3>📊 Upload Statistics</h3>
            <p id="stats-content">Loading...</p>
        </div>

        <div class="icon-grid" id="icon-grid">
            <!-- Icons will be loaded here -->
        </div>
    </div>

    <script>
        // Load and display icons
        fetch('upload-results.json')
            .then(response => response.json())
            .then(data => {
                const iconGrid = document.getElementById('icon-grid');
                const statsContent = document.getElementById('stats-content');

                if (Array.isArray(data) && data.length > 0) {
                    // Display stats
                    const successCount = data.filter(item => item.success).length;
                    const totalCount = data.length;
                    statsContent.innerHTML = `
                        <p><strong>Total Icons:</strong> ${totalCount}</p>
                        <p><strong>Successfully Uploaded:</strong> ${successCount}</p>
                        <p><strong>Success Rate:</strong> ${Math.round((successCount/totalCount)*100)}%</p>
                    `;

                    // Display icons
                    data.forEach(item => {
                        if (item.success && item.file) {
                            const iconItem = document.createElement('div');
                            iconItem.className = 'icon-item';
                            iconItem.innerHTML = `
                                <div class="icon-preview">
                                    <img src="${item.file.publicURL}" alt="${item.file.originalName}" onerror="this.style.display='none'">
                                </div>
                                <div class="icon-name">${item.file.originalName}</div>
                                <div class="icon-url">${item.file.publicURL}</div>
                                <div class="icon-id">ID: ${item.file.id}</div>
                            `;
                            iconGrid.appendChild(iconItem);
                        }
                    });
                } else {
                    statsContent.innerHTML = '<p>No upload results found</p>';
                }
            })
            .catch(error => {
                console.error('Error loading results:', error);
                document.getElementById('stats-content').innerHTML = '<p>Error loading results</p>';
            });
    </script>
</body>
</html>
EOF

    print_success "HTML preview generated: $html_file"
    print_info "Open $html_file in your browser to view the uploaded icons"
}

# Main function
main() {
    print_step "XBIT CDN Service - Icon Upload Tool"
    echo "========================================"

    # Check if service is running
    if ! check_service; then
        exit 1
    fi

    # Get authentication token
    local token
    if ! token=$(get_auth_token); then
        exit 1
    fi

    # Upload all icons
    if ! upload_all_icons "$token"; then
        print_warning "Some uploads failed, but continuing..."
    fi

    # Generate HTML preview
    generate_html_preview "$RESULTS_FILE"

    print_step "Upload process completed!"
    print_info "Check the results in: $RESULTS_FILE"
    print_info "View preview in: icon-upload-preview.html"
}

# Run main function
main "$@"
