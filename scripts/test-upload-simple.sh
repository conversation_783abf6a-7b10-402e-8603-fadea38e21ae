#!/bin/bash

# Simple test script to upload an icon without database dependency
# This script tests the upload endpoint directly

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Configuration
BASE_URL="http://localhost:8080"
UPLOAD_URL="$BASE_URL/api/cdn-service/upload"
AUTH_URL="$BASE_URL/auth/login"

# Get icon name from argument or use default
ICON_NAME="${1:-calendar.svg}"
ICON_PATH="images/icons/$ICON_NAME"

echo -e "${YELLOW}🚀 Testing upload of $ICON_NAME (Simple Test)${NC}"

# Check if file exists
if [ ! -f "$ICON_PATH" ]; then
    echo -e "${RED}❌ File $ICON_PATH not found${NC}"
    exit 1
fi

# Check if service is running
echo "Checking if service is running..."
if ! curl -s -f "$BASE_URL/api/cdn-service/graphql/healthz" > /dev/null 2>&1; then
    echo -e "${RED}❌ CDN service is not running${NC}"
    echo "Please start with: make run-local"
    exit 1
fi

# Get auth token
echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST "$AUTH_URL" \
    -H "Content-Type: application/json" \
    -d '{
        "username": "admin",
        "password": "admin123"
    }')

TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.token // empty' 2>/dev/null)

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
    echo -e "${RED}❌ Failed to get authentication token${NC}"
    echo "Response: $AUTH_RESPONSE"
    exit 1
fi

echo -e "${GREEN}✅ Authentication successful${NC}"

# Test the upload endpoint with a simple file
echo "Testing upload endpoint..."
RESPONSE=$(curl -s -X POST "$UPLOAD_URL" \
    -H "Authorization: Bearer $TOKEN" \
    -F "file=@$ICON_PATH" \
    -F "fileType=IMAGE" \
    -F "tags=icon,svg,test" \
    -F "metadata={\"type\": \"icon\", \"format\": \"svg\", \"test\": true}")

echo "Response: $RESPONSE"

# Check if we get a proper error about database (which means validation passed)
if echo "$RESPONSE" | grep -q "file extension not allowed"; then
    echo -e "${RED}❌ File extension validation failed${NC}"
    exit 1
elif echo "$RESPONSE" | grep -q "relation.*does not exist"; then
    echo -e "${GREEN}✅ File extension validation passed!${NC}"
    echo -e "${YELLOW}⚠️  Database table doesn't exist, but upload validation works${NC}"
    echo "This means the SVG extension is now properly supported!"
    exit 0
elif echo "$RESPONSE" | grep -q "success.*true"; then
    echo -e "${GREEN}✅ Upload successful!${NC}"
    exit 0
else
    echo -e "${YELLOW}⚠️  Unexpected response${NC}"
    echo "Response: $RESPONSE"
    exit 1
fi
