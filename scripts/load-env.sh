#!/bin/bash

# Load environment variables for XBIT CDN Service
# Usage: source ./scripts/load-env.sh [environment]

ENV=${1:-local}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Environment file paths
ENV_FILE="$PROJECT_DIR/env/.env.$ENV"
DEFAULT_ENV_FILE="$PROJECT_DIR/.env"

# Load environment variables
if [ -f "$ENV_FILE" ]; then
    echo "Loading environment from $ENV_FILE"
    set -a
    source "$ENV_FILE"
    set +a
elif [ -f "$DEFAULT_ENV_FILE" ]; then
    echo "Loading environment from $DEFAULT_ENV_FILE"
    set -a
    source "$DEFAULT_ENV_FILE"
    set +a
else
    echo "No environment file found for $ENV"
    echo "Expected: $ENV_FILE or $DEFAULT_ENV_FILE"
fi

# Set default values if not set (for backward compatibility with scripts)
export SYSTEM_ENV=${SYSTEM_ENV:-$ENV}
export SYSTEM_ADDR=${SYSTEM_ADDR:-8080}

# Database defaults (using proper Viper variable names)
export PGSQL_PATH=${PGSQL_PATH:-127.0.0.1}
export PGSQL_PORT=${PGSQL_PORT:-5433}
export PGSQL_DB_NAME=${PGSQL_DB_NAME:-xbit_cdn}
export PGSQL_USERNAME=${PGSQL_USERNAME:-postgres}
export PGSQL_PASSWORD=${PGSQL_PASSWORD:-postgres}
export PGSQL_CONFIG=${PGSQL_CONFIG:-sslmode=disable}

# Construct DATABASE_URL if not set (for scripts that need it)
if [ -z "$DATABASE_URL" ]; then
    export DATABASE_URL="postgres://${PGSQL_USERNAME}:${PGSQL_PASSWORD}@${PGSQL_PATH}:${PGSQL_PORT}/${PGSQL_DB_NAME}?${PGSQL_CONFIG}"
fi

echo "Environment: $APP_ENV"
echo "Port: $SERVER_PORT"
echo "Database: $POSTGRES_DB"
