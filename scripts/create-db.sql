-- Create database and table for XBIT CDN Service
CREATE DATABASE IF NOT EXISTS xbit_cdn;

\c xbit_cdn;

-- Create files table
CREATE TABLE IF NOT EXISTS files (
    id VARCHAR(255) PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    size BIGINT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'UPLOADING',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    public_url TEXT,
    cdn_url TEXT,
    tags TEXT[],
    metadata TEXT,
    user_id VARCHAR(255)
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_files_user_id ON files(user_id);
CREATE INDEX IF NOT EXISTS idx_files_status ON files(status);
CREATE INDEX IF NOT EXISTS idx_files_file_type ON files(file_type);
CREATE INDEX IF NOT EXISTS idx_files_uploaded_at ON files(uploaded_at);

-- Insert some test data
INSERT INTO files (id, filename, original_name, file_type, mime_type, size, status, public_url, tags)
VALUES
    ('test-1', 'test-1.jpg', 'test-image.jpg', 'IMAGE', 'image/jpeg', 1024, 'READY', 'https://example.com/test-1.jpg', ARRAY['test', 'image']),
    ('test-2', 'test-2.svg', 'test-icon.svg', 'IMAGE', 'image/svg+xml', 2048, 'READY', 'https://example.com/test-2.svg', ARRAY['test', 'icon', 'svg'])
ON CONFLICT (id) DO NOTHING;
