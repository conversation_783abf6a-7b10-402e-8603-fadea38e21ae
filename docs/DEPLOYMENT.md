# XBIT CDN Service - Deployment Guide

This comprehensive guide covers deployment strategies for the XBIT CDN Service across different environments: **Local**, **Unstable**, **Staging**, and **Production**.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Environment Overview](#environment-overview)
- [Local Development](#local-development)
- [Unstable Environment](#unstable-environment)
- [Staging Environment](#staging-environment)
- [Production Environment](#production-environment)
- [Database Management](#database-management)
- [Monitoring and Logging](#monitoring-and-logging)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### General Requirements
- Docker and Docker Compose v2.0+
- Go 1.21+ (for local development)
- Make utility
- Git

### Environment-Specific Requirements
- **Local**: PostgreSQL, Redis (via Docker)
- **Unstable/Staging**: External database, Redis cluster
- **Production**: Managed database service, Redis cluster, Load balancer, SSL certificates

### External Services
- Cloudflare R2 bucket and credentials
- Cloudflare CDN (optional but recommended)
- Domain name and SSL certificates (for non-local environments)

## Environment Overview

| Environment | Purpose | Database | Redis | SSL | Monitoring |
|-------------|---------|----------|-------|-----|------------|
| **Local** | Development | Docker PostgreSQL | Docker Redis | No | Basic |
| **Unstable** | Feature testing | External DB | External Redis | Yes | Enhanced |
| **Staging** | Pre-production testing | External DB | External Redis | Yes | Full |
| **Production** | Live service | Managed DB | Redis Cluster | Yes | Full + Alerts |

## Local Development

### Quick Start

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd xbit-cdn-service
   make setup-local
   ```

2. **Configure environment**:
   ```bash
   # Edit env/.env.local with your settings
   vim env/.env.local
   ```

3. **Start services**:
   ```bash
   make local-up
   ```

4. **Run migrations**:
   ```bash
   make db-migrate-local
   ```

5. **Start development server**:
   ```bash
   make dev
   ```

### Local Environment Configuration

The local environment uses the following default settings:

```bash
# Server Configuration
ENV=local
PORT=8080
HOST=localhost
DEBUG=true
LOG_LEVEL=debug

# Database Configuration (Docker PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres123
DB_NAME=xbit_cdn_local
DB_SSLMODE=disable
DB_MAX_CONNECTIONS=10
DB_MAX_IDLE_CONNECTIONS=5
```

### Local Development Commands

```bash
# Environment management
make local-up          # Start all services
make local-down        # Stop all services
make local-logs        # View logs

# Database management
make db-migrate-local  # Run migrations
make db-reset          # Reset database

# Development
make dev               # Start with hot reload
make test              # Run tests
make lint              # Run linter
```

## Unstable Environment

The unstable environment is used for testing new features and experimental changes.

### Setup Unstable Environment

1. **Configure environment**:
   ```bash
   make setup-unstable
   # Edit env/.env.unstable with your credentials
   ```

2. **Deploy to unstable**:
   ```bash
   make unstable-up
   make db-migrate-unstable
   ```

### Unstable Environment Configuration

```bash
# Server Configuration
ENV=unstable
PORT=8080
HOST=0.0.0.0
DEBUG=true
LOG_LEVEL=info

# Database Configuration (External)
DB_HOST=unstable-db.internal
DB_PORT=5432
DB_USER=xbit_user
DB_PASSWORD=CHANGE_ME_UNSTABLE_DB_PASSWORD
DB_NAME=xbit_cdn_unstable
DB_SSLMODE=require
DB_MAX_CONNECTIONS=20
DB_MAX_IDLE_CONNECTIONS=10

# Cloudflare R2 Configuration
R2_BUCKET_NAME=xbit-cdn-unstable
CDN_BASE_URL=https://unstable-cdn.xbit.com

# Security
ENABLE_HTTPS=true
HSTS_ENABLED=true
CSRF_PROTECTION=true
RATE_LIMITING_ENABLED=true
REQUESTS_PER_MINUTE=1000
```

### Unstable Commands

```bash
make unstable-up           # Deploy unstable environment
make unstable-down         # Stop unstable environment
make unstable-logs         # View unstable logs
make db-migrate-unstable   # Run unstable migrations
make health-unstable       # Check unstable health
```

## Staging Environment

The staging environment mirrors production for final testing before deployment.

### Setup Staging Environment

1. **Configure environment**:
   ```bash
   make setup-staging
   # Edit env/.env.staging with production-like settings
   ```

2. **Deploy to staging**:
   ```bash
   make staging-up
   make db-migrate-staging
   ```

### Staging Environment Configuration

```bash
# Server Configuration
ENV=staging
PORT=8080
HOST=0.0.0.0
DEBUG=false
LOG_LEVEL=info

# Database Configuration (External)
DB_HOST=staging-db.internal
DB_PORT=5432
DB_USER=xbit_user
DB_PASSWORD=CHANGE_ME_STAGING_DB_PASSWORD
DB_NAME=xbit_cdn_staging
DB_SSLMODE=require
DB_MAX_CONNECTIONS=50
DB_MAX_IDLE_CONNECTIONS=25

# Enhanced Configuration
MAX_FILE_SIZE=1GB
SIGNED_URL_EXPIRY=4h
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Monitoring
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
LOG_FORMAT=json
LOG_OUTPUT=file
```

### Staging Commands

```bash
make staging-up           # Deploy staging environment
make staging-down         # Stop staging environment
make staging-logs         # View staging logs
make db-migrate-staging   # Run staging migrations
make health-staging       # Check staging health
## Production Environment

The production environment is the live service with full security, monitoring, and high availability.

### Setup Production Environment

1. **Configure environment**:
   ```bash
   make setup-production
   # Edit env/.env.production with production credentials
   ```

2. **Deploy to production** (handled by DevOps CI/CD):
   ```bash
   # Production deployment is typically handled by CI/CD pipeline
   # Manual deployment (if needed):
   make production-up
   make db-migrate-production
   ```

### Production Environment Configuration

```bash
# Server Configuration
ENV=production
PORT=8080
HOST=0.0.0.0
DEBUG=false
LOG_LEVEL=warn

# Database Configuration (Managed Service)
DB_HOST=CHANGE_ME_PRODUCTION_DB_HOST
DB_PORT=5432
DB_USER=CHANGE_ME_PRODUCTION_DB_USER
DB_PASSWORD=CHANGE_ME_PRODUCTION_DB_PASSWORD
DB_NAME=CHANGE_ME_PRODUCTION_DB_NAME
DB_SSLMODE=require
DB_MAX_CONNECTIONS=100
DB_MAX_IDLE_CONNECTIONS=50
DB_CONNECTION_TIMEOUT=30s
DB_IDLE_TIMEOUT=10m

# Enhanced Security
JWT_EXPIRY=12h
API_KEY_REQUIRED=true
RATE_LIMITING_ENABLED=true
REQUESTS_PER_MINUTE=200
BURST_SIZE=20

# Production Features
MAX_FILE_SIZE=2GB
SIGNED_URL_EXPIRY=6h
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 1 * * *
BACKUP_RETENTION_DAYS=90
S3_BACKUP_ENABLED=true

# Monitoring & Logging
LOG_STRUCTURED=true
LOG_FORMAT=json
LOG_OUTPUT=file
LOG_FILE_PATH=/var/log/xbit-cdn/app.log
REDIS_CLUSTER_MODE=true
```

### Production Commands

```bash
make production-up           # Deploy production (use with caution)
make production-down         # Stop production (emergency only)
make production-logs         # View production logs
make db-migrate-production   # Run production migrations
make health-production       # Check production health
```

### Production Database Recommendations

For production, use managed database services:

- **AWS RDS PostgreSQL**
- **Google Cloud SQL for PostgreSQL**
- **Azure Database for PostgreSQL**
- **DigitalOcean Managed Databases**
- **Supabase** (for smaller deployments)

## Database Management

### Migration Commands by Environment

```bash
# Local environment
make db-migrate-local      # Run migrations on local DB
make db-reset             # Reset local database

# Unstable environment
make db-migrate-unstable  # Run migrations on unstable DB

# Staging environment
make db-migrate-staging   # Run migrations on staging DB

# Production environment
make db-migrate-production # Run migrations on production DB
```

### Database Connection Examples

#### Local Development
```bash
# Connect to local PostgreSQL
psql "postgresql://postgres:postgres@127.0.0.1:5433/xbit_cdn"
```

#### Remote Environments
```bash
# Connect to unstable database
psql "postgresql://xbit_user:<EMAIL>:5432/xbit_cdn_unstable?sslmode=require"

# Connect to staging database
psql "postgresql://xbit_user:<EMAIL>:5432/xbit_cdn_staging?sslmode=require"

# Connect to production database
psql "postgresql://xbit_user:<EMAIL>:5432/xbit_cdn_production?sslmode=require"
```

## Monitoring and Logging

### Health Checks by Environment

```bash
make health-local         # Check local service health
make health-unstable      # Check unstable service health
make health-staging       # Check staging service health
make health-production    # Check production service health
```

### Log Management

#### Local Development
```bash
make local-logs           # View all local service logs
docker logs xbit-cdn-service-local -f  # Follow specific service logs
```

#### Remote Environments
```bash
make unstable-logs        # View unstable environment logs
make staging-logs         # View staging environment logs
make production-logs      # View production environment logs
```

### Monitoring Endpoints

| Environment | Health Check | Metrics | Grafana |
|-------------|-------------|---------|---------|
| **Local** | http://localhost:8080/health | http://localhost:8080/metrics | N/A |
| **Unstable** | https://unstable-api.xbit.com/health | https://unstable-api.xbit.com/metrics | N/A |
| **Staging** | https://staging-api.xbit.com/health | https://staging-api.xbit.com/metrics | https://staging-grafana.xbit.com |
| **Production** | https://api.xbit.com/health | https://api.xbit.com/metrics | https://grafana.xbit.com |

services:
  xbit-cdn-service:
    image: your-registry/xbit-cdn-service:latest
    container_name: xbit-cdn-service-prod
    ports:
      - "8080:8080"
    environment:
      ENV: production
      # ... production environment variables
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    container_name: xbit-cdn-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - xbit-cdn-service
    restart: unless-stopped
```

2. **Deploy to production**

```bash
# Build and push image
docker build -t your-registry/xbit-cdn-service:latest .
docker push your-registry/xbit-cdn-service:latest

# Deploy on production server
docker-compose -f docker-compose.prod.yml up -d
```

### Option 2: Kubernetes

#### 1. Create Kubernetes manifests

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: xbit-cdn

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: xbit-cdn-config
  namespace: xbit-cdn
data:
  PORT: "8080"
  ENV: "production"
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  DB_NAME: "xbit_cdn"

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: xbit-cdn-secrets
  namespace: xbit-cdn
type: Opaque
stringData:
  DB_PASSWORD: "your-db-password"
  R2_ACCESS_KEY_ID: "your-r2-access-key"
  R2_SECRET_ACCESS_KEY: "your-r2-secret-key"
  JWT_SECRET: "your-jwt-secret"

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: xbit-cdn-service
  namespace: xbit-cdn
spec:
  replicas: 3
  selector:
    matchLabels:
      app: xbit-cdn-service
  template:
    metadata:
      labels:
        app: xbit-cdn-service
    spec:
      containers:
      - name: xbit-cdn-service
        image: your-registry/xbit-cdn-service:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: xbit-cdn-config
        - secretRef:
            name: xbit-cdn-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: xbit-cdn-service
  namespace: xbit-cdn
spec:
  selector:
    app: xbit-cdn-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: xbit-cdn-ingress
  namespace: xbit-cdn
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: xbit-cdn-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: xbit-cdn-service
            port:
              number: 80
```

#### 2. Deploy to Kubernetes

```bash
# Apply all manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n xbit-cdn
kubectl get services -n xbit-cdn
kubectl get ingress -n xbit-cdn

# View logs
kubectl logs -f deployment/xbit-cdn-service -n xbit-cdn
```

### Option 3: Cloud Platforms

#### AWS ECS

1. **Create task definition**
2. **Set up ECS service**
3. **Configure Application Load Balancer**
4. **Set up RDS for database**

#### Google Cloud Run

```bash
# Build and push to Google Container Registry
gcloud builds submit --tag gcr.io/PROJECT-ID/xbit-cdn-service

# Deploy to Cloud Run
gcloud run deploy xbit-cdn-service \
  --image gcr.io/PROJECT-ID/xbit-cdn-service \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars ENV=production,PORT=8080
```

#### Azure Container Instances

```bash
# Create resource group
az group create --name xbit-cdn-rg --location eastus

# Deploy container
az container create \
  --resource-group xbit-cdn-rg \
  --name xbit-cdn-service \
  --image your-registry/xbit-cdn-service:latest \
  --dns-name-label xbit-cdn \
  --ports 8080 \
  --environment-variables ENV=production PORT=8080
```

## SSL/TLS Configuration

### Let's Encrypt with Certbot

```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Manual SSL Configuration

```nginx
# nginx.prod.conf
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://xbit-cdn-service:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## Monitoring and Logging

### Health Checks

```bash
# Application health
curl -f http://localhost:8080/health

# Database health
docker exec xbit-cdn-postgres pg_isready -U postgres

# Container health
docker ps --filter "name=xbit-cdn"
```

### Logging

#### Centralized Logging with ELK Stack

```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:7.14.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf

  kibana:
    image: docker.elastic.co/kibana/kibana:7.14.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

#### Application Logging

```go
// Add to your Go application
import (
    "github.com/sirupsen/logrus"
)

func init() {
    logrus.SetFormatter(&logrus.JSONFormatter{})
    logrus.SetLevel(logrus.InfoLevel)
}
```

## Backup and Recovery

### Database Backup

```bash
# Create backup
docker exec xbit-cdn-postgres pg_dump -U postgres xbit_cdn > backup.sql

# Restore backup
docker exec -i xbit-cdn-postgres psql -U postgres xbit_cdn < backup.sql

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker exec xbit-cdn-postgres pg_dump -U postgres xbit_cdn > "backup_${DATE}.sql"
aws s3 cp "backup_${DATE}.sql" s3://your-backup-bucket/
```

### File Storage Backup

Since files are stored in Cloudflare R2, they are automatically replicated. However, you can create additional backups:

```bash
# Sync R2 bucket to another location
rclone sync r2:your-bucket s3:backup-bucket
```

## Performance Optimization

### Database Optimization

```sql
-- Add indexes for better query performance
CREATE INDEX CONCURRENTLY idx_files_user_id_status ON files(user_id, status);
CREATE INDEX CONCURRENTLY idx_files_created_at ON files(created_at DESC);

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM files WHERE user_id = 'user-id' AND status = 'READY';
```

### Application Optimization

1. **Connection Pooling**
   ```go
   db.SetMaxOpenConns(25)
   db.SetMaxIdleConns(25)
   db.SetConnMaxLifetime(5 * time.Minute)
   ```

2. **Caching with Redis**
   ```go
   // Cache file metadata
   rdb.Set(ctx, "file:"+fileID, fileJSON, time.Hour)
   ```

3. **CDN Configuration**
   - Set appropriate cache headers
   - Configure edge caching rules
   - Use image optimization

## Security Considerations

### Network Security

1. **Firewall Rules**
   - Only allow necessary ports (80, 443, 22)
   - Restrict database access to application servers only

2. **VPC/Network Isolation**
   - Use private subnets for databases
   - Implement security groups/network policies

### Application Security

1. **Environment Variables**
   - Never commit secrets to version control
   - Use secret management services (AWS Secrets Manager, etc.)

2. **JWT Security**
   - Use strong secret keys
   - Implement token rotation
   - Set appropriate expiration times

3. **File Upload Security**
   - Validate file types and sizes
   - Scan for malware
   - Use signed URLs for sensitive content

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database connectivity
   docker exec xbit-cdn-service nc -zv postgres 5432

   # Check database logs
   docker logs xbit-cdn-postgres
   ```

2. **R2 Upload Failures**
   ```bash
   # Test R2 connectivity
   aws s3 ls --endpoint-url=https://your-account.r2.cloudflarestorage.com

   # Check credentials
   aws configure list
   ```

3. **High Memory Usage**
   ```bash
   # Monitor container resources
   docker stats xbit-cdn-service

   # Check for memory leaks
   go tool pprof http://localhost:8080/debug/pprof/heap
   ```

### Debugging

1. **Enable Debug Logging**
   ```bash
   export LOG_LEVEL=debug
   ```

2. **Health Check Endpoints**
   ```bash
   curl http://localhost:8080/health
   curl http://localhost:8080/debug/vars
   ```

3. **Database Queries**
   ```sql
   -- Check active connections
   SELECT * FROM pg_stat_activity WHERE datname = 'xbit_cdn';

   -- Check slow queries
   SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;
   ```

## Rollback Strategy

### Application Rollback

```bash
# Rollback to previous version
docker-compose down
docker pull your-registry/xbit-cdn-service:previous-tag
docker-compose up -d

# Kubernetes rollback
kubectl rollout undo deployment/xbit-cdn-service -n xbit-cdn
```

### Database Rollback

```bash
# Restore from backup
docker exec -i xbit-cdn-postgres psql -U postgres xbit_cdn < backup_previous.sql
```

## Maintenance

### Regular Maintenance Tasks

1. **Update Dependencies**
   ```bash
   go get -u ./...
   go mod tidy
   ```

2. **Database Maintenance**
   ```sql
   -- Vacuum and analyze
   VACUUM ANALYZE files;

   -- Reindex
   REINDEX TABLE files;
   ```

3. **Log Rotation**
   ```bash
   # Configure logrotate
   /var/log/xbit-cdn/*.log {
       daily
       rotate 30
       compress
       delaycompress
       missingok
       notifempty
   }
   ```

---

## Summary

This comprehensive deployment guide covers the XBIT CDN Service deployment across four distinct environments:

### Environment Summary

| Environment | Purpose | Key Features |
|-------------|---------|--------------|
| **Local** | Development | Docker containers, hot reload, debug logging |
| **Unstable** | Feature testing | External services, SSL, enhanced monitoring |
| **Staging** | Pre-production | Production-like config, full monitoring, backups |
| **Production** | Live service | Managed services, clustering, full security |

### Key Commands Reference

```bash
# Environment Setup
make setup-local          # Setup local development
make setup-unstable       # Setup unstable environment
make setup-staging        # Setup staging environment
make setup-production     # Setup production environment

# Environment Management
make local-up/down        # Manage local environment
make unstable-up/down     # Manage unstable environment
make staging-up/down      # Manage staging environment
make production-up/down   # Manage production environment

# Database Management
make db-migrate-local     # Local database migrations
make db-migrate-unstable  # Unstable database migrations
make db-migrate-staging   # Staging database migrations
make db-migrate-production # Production database migrations

# Health Checks
make health-local         # Check local service health
make health-unstable      # Check unstable service health
make health-staging       # Check staging service health
make health-production    # Check production service health
```

### DevOps Integration

The CI/CD pipeline will handle automated deployments using the GitLab CI configuration template provided. Manual deployment commands are available for emergency situations or local testing.

### Next Steps

1. Configure environment-specific credentials in `env/` directory
2. Set up external database and Redis services for non-local environments
3. Configure SSL certificates for HTTPS environments
4. Set up monitoring and alerting systems
5. Test deployment procedures in each environment
6. Document environment-specific procedures and contacts

For questions or issues, refer to the troubleshooting section or contact the development team.

4. **Certificate Renewal**
   ```bash
   # Check certificate expiry
   openssl x509 -in /etc/nginx/ssl/cert.pem -text -noout | grep "Not After"

   # Renew Let's Encrypt certificates
   certbot renew
   ```
