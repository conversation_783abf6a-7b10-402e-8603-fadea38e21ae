# XBIT CDN Service - Production Environment Configuration Template
# Copy this file to .env.production and update the values

# System Configuration
# Maps to system.* in config.yaml via Viper
SYSTEM_ENV=production
SYSTEM_ADDR=8080
SYSTEM_ROUTER_PREFIX=/api/cdn
SYSTEM_GRAPHQL_PREFIX=/api/cdn/graphql

# Database Configuration
# Maps to pgsql.* in config.yaml via Viper
PGSQL_PATH=127.0.0.1
PGSQL_PORT=5433
PGSQL_DB_NAME=xbit_cdn
PGSQL_USERNAME=postgres
PGSQL_PASSWORD=postgres
PGSQL_CONFIG=sslmode=disable
PGSQL_MAX_IDLE_CONNS=50
PGSQL_MAX_OPEN_CONNS=200

# JWT Configuration
# Maps to jwt.* in config.yaml via Viper
JWT_SIGNING_KEY=CHANGE_ME_PRODUCTION_JWT_SECRET
JWT_EXPIRES_TIME=12h
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-cdn-service

# Cloudflare R2 Configuration
# Maps to r2.* in config.yaml via Viper
R2_ACCOUNT_ID=CHANGE_ME_R2_ACCOUNT_ID
R2_ACCESS_KEY_ID=CHANGE_ME_R2_ACCESS_KEY_ID
R2_SECRET_ACCESS_KEY=CHANGE_ME_R2_SECRET_ACCESS_KEY
R2_BUCKET_NAME=CHANGE_ME_R2_BUCKET_NAME
R2_REGION=auto
R2_ENDPOINT=

# CDN Configuration
# Maps to cdn.* in config.yaml via Viper
CDN_ENABLED=true
CDN_PROVIDER=cloudflare
CDN_BASE_URL=CHANGE_ME_CDN_BASE_URL
CDN_API_TOKEN=CHANGE_ME_CDN_API_TOKEN
CDN_ZONE_ID=CHANGE_ME_CDN_ZONE_ID
CDN_DOMAIN=CHANGE_ME_CDN_DOMAIN
CDN_CACHE_TTL=86400
CDN_IMAGE_OPTIMIZE=true

# Upload Configuration
# Maps to upload.* in config.yaml via Viper
UPLOAD_MAX_FILE_SIZE=**********
UPLOAD_ALLOWED_EXTENSIONS=.jpg,.jpeg,.png,.gif,.webp,.svg,.mp4,.mov,.avi,.webm,.pdf,.doc,.docx,.zip,.rar
UPLOAD_SIGNED_URL_EXPIRY=21600
UPLOAD_CHUNK_SIZE=********

# Logging Configuration
# Maps to zap.* in config.yaml via Viper
ZAP_LEVEL=warn
ZAP_FORMAT=json
ZAP_PREFIX=[xbit-cdn-service]
ZAP_DIRECTOR=log
ZAP_ENCODE_LEVEL=LowercaseLevelEncoder
ZAP_STACKTRACE_KEY=stacktrace
ZAP_MAX_AGE=30
ZAP_SHOW_LINE=false
ZAP_LOG_IN_CONSOLE=false
