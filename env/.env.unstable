# XBIT CDN Service - Unstable Environment Configuration

# System Configuration
# Maps to system.* in config.yaml via Viper
SYSTEM_ENV=unstable
SYSTEM_ADDR=8080
SYSTEM_ROUTER_PREFIX=/api/cdn
SYSTEM_GRAPHQL_PREFIX=/api/cdn/graphql

# Database Configuration
# Maps to pgsql.* in config.yaml via Viper
PGSQL_PATH=127.0.0.1
PGSQL_PORT=5433
PGSQL_DB_NAME=xbit_cdn
PGSQL_USERNAME=postgres
PGSQL_PASSWORD=postgres
PGSQL_CONFIG=sslmode=disable
PGSQL_MAX_IDLE_CONNS=20
PGSQL_MAX_OPEN_CONNS=50

# JWT Configuration
# Maps to jwt.* in config.yaml via Viper
JWT_SIGNING_KEY=CHANGE_ME_UNSTABLE_JWT_SECRET_KEY
JWT_EXPIRES_TIME=24h
JWT_BUFFER_TIME=168h
JWT_ISSUER=xbit-cdn-service

# Cloudflare R2 Configuration
# Maps to r2.* in config.yaml via Viper
R2_ACCOUNT_ID=23730d7e6dec9dfe0266e9df554e20c5
R2_ACCESS_KEY_ID=659727ae7f867e3a8430d26d8200b6dc
R2_SECRET_ACCESS_KEY=3a91eb858bd3335ba0c7855fec0322794171a38d1aaec5794f931b0c489faa50
R2_BUCKET_NAME=xbit-unstable
R2_REGION=auto
R2_ENDPOINT=https://23730d7e6dec9dfe0266e9df554e20c5.r2.cloudflarestorage.com

# CDN Configuration
# Maps to cdn.* in config.yaml via Viper
CDN_ENABLED=true
CDN_PROVIDER=cloudflare
CDN_BASE_URL=https://unstable-cdn.xbit.com
CDN_API_TOKEN=CHANGE_ME_CDN_API_TOKEN
CDN_ZONE_ID=CHANGE_ME_CDN_ZONE_ID
CDN_DOMAIN=unstable-cdn.xbit.com
CDN_CACHE_TTL=3600
CDN_IMAGE_OPTIMIZE=true

# Upload Configuration
# Maps to upload.* in config.yaml via Viper
UPLOAD_MAX_FILE_SIZE=*********
UPLOAD_ALLOWED_EXTENSIONS=.jpg,.jpeg,.png,.gif,.svg,.mp4,.mov,.avi,.webm,.pdf,.doc,.docx,.zip,.rar
UPLOAD_SIGNED_URL_EXPIRY=7200
UPLOAD_CHUNK_SIZE=5242880

# Logging Configuration
# Maps to zap.* in config.yaml via Viper
ZAP_LEVEL=info
ZAP_FORMAT=console
ZAP_PREFIX=[xbit-cdn-service]
ZAP_DIRECTOR=log
ZAP_ENCODE_LEVEL=LowercaseColorLevelEncoder
ZAP_STACKTRACE_KEY=stacktrace
ZAP_MAX_AGE=7
ZAP_SHOW_LINE=true
ZAP_LOG_IN_CONSOLE=true
