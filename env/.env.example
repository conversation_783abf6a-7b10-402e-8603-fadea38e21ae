# XBIT CDN Service - Example Environment Configuration
# Copy this file to .env.local and update the values

# System Configuration
# Maps to system.* in config.yaml via Viper
SYSTEM_ENV=local
SYSTEM_ADDR=8080
SYSTEM_ROUTER_PREFIX=/api/cdn
SYSTEM_GRAPHQL_PREFIX=/api/cdn/graphql

# Database Configuration
# Maps to pgsql.* in config.yaml via Viper
PGSQL_PATH=127.0.0.1
PGSQL_PORT=5433
PGSQL_DB_NAME=xbit_cdn
PGSQL_USERNAME=postgres
PGSQL_PASSWORD=postgres
PGSQL_CONFIG=sslmode=disable
PGSQL_MAX_IDLE_CONNS=10
PGSQL_MAX_OPEN_CONNS=100

# JWT Configuration
# Maps to jwt.* in config.yaml via Viper
JWT_SIGNING_KEY=your_jwt_secret_key
JWT_EXPIRES_TIME=24h
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-cdn-service

# Cloudflare R2 Configuration
# Maps to r2.* in config.yaml via Viper
R2_ACCOUNT_ID=your_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_BUCKET_NAME=your_bucket_name
R2_REGION=auto
R2_ENDPOINT=https://your_account_id.r2.cloudflarestorage.com

# CDN Configuration
# Maps to cdn.* in config.yaml via Viper
CDN_ENABLED=false
CDN_PROVIDER=cloudflare
CDN_BASE_URL=https://your-cdn-domain.com
CDN_API_TOKEN=your_api_token
CDN_ZONE_ID=your_zone_id
CDN_DOMAIN=your-domain.com
CDN_CACHE_TTL=3600
CDN_IMAGE_OPTIMIZE=true

# Upload Configuration
# Maps to upload.* in config.yaml via Viper
UPLOAD_MAX_FILE_SIZE=*********
UPLOAD_ALLOWED_EXTENSIONS=.jpg,.jpeg,.png,.gif,.mp4,.mov,.avi,.webm
UPLOAD_SIGNED_URL_EXPIRY=3600
UPLOAD_CHUNK_SIZE=5242880

# Logging Configuration
# Maps to zap.* in config.yaml via Viper
ZAP_LEVEL=info
ZAP_FORMAT=console
ZAP_PREFIX=[xbit-cdn-service]
ZAP_DIRECTOR=log
ZAP_ENCODE_LEVEL=LowercaseColorLevelEncoder
ZAP_STACKTRACE_KEY=stacktrace
ZAP_MAX_AGE=0
ZAP_SHOW_LINE=true
ZAP_LOG_IN_CONSOLE=true
