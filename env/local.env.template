# XBIT CDN Service - Local Environment Configuration Template
# Copy this file to .env.local and update the values

# System Configuration
# Maps to system.* in config.yaml via Viper
SYSTEM_ENV=local
SYSTEM_ADDR=8080
SYSTEM_ROUTER_PREFIX=/api/cdn
SYSTEM_GRAPHQL_PREFIX=/api/cdn/graphql

# Database Configuration
# Maps to pgsql.* in config.yaml via Viper
PGSQL_PATH=127.0.0.1
PGSQL_PORT=5433
PGSQL_DB_NAME=xbit_cdn
PGSQL_USERNAME=postgres
PGSQL_PASSWORD=postgres
PGSQL_CONFIG=sslmode=disable
PGSQL_MAX_IDLE_CONNS=10
PGSQL_MAX_OPEN_CONNS=100

# JWT Configuration
# Maps to jwt.* in config.yaml via Viper
JWT_SIGNING_KEY=xbit-cdn-service-local-secret
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-cdn-service

# Cloudflare R2 Configuration
# Maps to r2.* in config.yaml via Viper
R2_ACCOUNT_ID=
R2_ACCESS_KEY_ID=
R2_SECRET_ACCESS_KEY=
R2_BUCKET_NAME=
R2_REGION=auto
R2_ENDPOINT=

# CDN Configuration
# Maps to cdn.* in config.yaml via Viper
CDN_ENABLED=false
CDN_PROVIDER=cloudflare
CDN_BASE_URL=
CDN_API_TOKEN=
CDN_ZONE_ID=
CDN_DOMAIN=
CDN_CACHE_TTL=3600
CDN_IMAGE_OPTIMIZE=true

# Upload Configuration
# Maps to upload.* in config.yaml via Viper
UPLOAD_MAX_FILE_SIZE=*********
UPLOAD_ALLOWED_EXTENSIONS=.jpg,.png,.gif,.webp,.pdf,.mp4
UPLOAD_SIGNED_URL_EXPIRY=3600
UPLOAD_CHUNK_SIZE=5242880

# Logging Configuration
# Maps to zap.* in config.yaml via Viper
ZAP_LEVEL=info
ZAP_FORMAT=console
ZAP_PREFIX=[xbit-cdn-service]
ZAP_DIRECTOR=log
ZAP_ENCODE_LEVEL=LowercaseColorLevelEncoder
ZAP_STACKTRACE_KEY=stacktrace
ZAP_MAX_AGE=0
ZAP_SHOW_LINE=true
ZAP_LOG_IN_CONSOLE=true
