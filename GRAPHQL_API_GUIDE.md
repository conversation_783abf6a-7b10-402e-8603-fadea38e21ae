# XBIT CDN Service - GraphQL API Guide

## 🚀 Tổng quan

XBIT CDN Service cung cấp một GraphQL API hoàn chỉnh cho việc upload, download và quản lý file ảnh. Service hoạt động như một trung gian giữa các service khác và Cloudflare R2 + CDN.

**Kiến trúc:**
```
Other Services → xbit-cdn-service → Cloudflare R2 + CDN
```

## 📡 Endpoints

| Endpoint | Mô tả |
|----------|-------|
| `POST /api/cdn-service/graphql` | GraphQL API chính |
| `GET /api/cdn-service/graphql/playground` | GraphQL Playground (development) |
| `GET /api/cdn-service/graphql/healthz` | Health check |
| `POST /auth/login` | Authentication |

## 🔐 Authentication

Hầu hết các operations yêu cầu JWT token trong header:

```bash
Authorization: Bearer <your-jwt-token>
```

### Lấy JWT Token:

```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

## 📋 GraphQL Schema

### File Types

```graphql
enum FileType {
  IMAGE
  VIDEO
}

enum FileStatus {
  UPLOADING
  PROCESSING
  READY
  ERROR
}

enum URLType {
  PUBLIC
  SIGNED
}
```

### Main Types

```graphql
type FileMetadata {
  id: ID!
  filename: String!
  originalName: String!
  fileType: FileType!
  mimeType: String!
  size: Int!
  status: FileStatus!
  uploadedAt: String!
  updatedAt: String!
  publicURL: String
  cdnURL: String
  tags: [String!]
  metadata: String
}

type UploadResponse {
  success: Boolean!
  message: String!
  file: FileMetadata
  uploadURL: String
}

type DownloadResponse {
  success: Boolean!
  message: String!
  url: String
  expiresAt: String
}
```

## 🔄 Queries

### 1. Health Check

```graphql
query {
  health
}
```

### 2. Get File by ID

```graphql
query GetFile($id: ID!) {
  file(id: $id) {
    id
    filename
    originalName
    fileType
    mimeType
    size
    status
    uploadedAt
    publicURL
    cdnURL
    tags
  }
}
```

### 3. List Files

```graphql
query ListFiles($filter: FileFilter, $pagination: PaginationInput) {
  files(filter: $filter, pagination: $pagination) {
    files {
      id
      filename
      fileType
      status
      uploadedAt
      publicURL
      cdnURL
    }
    totalCount
    hasNextPage
    hasPreviousPage
  }
}
```

**Variables:**
```json
{
  "filter": {
    "fileType": "IMAGE",
    "status": "READY"
  },
  "pagination": {
    "limit": 20,
    "offset": 0
  }
}
```

### 4. Generate Download URL

```graphql
query GenerateDownloadURL($id: ID!, $urlType: URLType, $expiresIn: Int) {
  downloadURL(id: $id, urlType: $urlType, expiresIn: $expiresIn) {
    success
    message
    url
    expiresAt
  }
}
```

**Variables:**
```json
{
  "id": "file-uuid-here",
  "urlType": "PUBLIC",
  "expiresIn": 3600
}
```

## ✏️ Mutations

### 1. Upload File (Signed URL - Recommended)

```graphql
mutation UploadFile($input: UploadInput!) {
  uploadFile(input: $input) {
    success
    message
    uploadURL
    file {
      id
      filename
      status
      publicURL
      cdnURL
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "filename": "my-image.jpg",
    "fileType": "IMAGE",
    "mimeType": "image/jpeg",
    "size": 1024000,
    "tags": ["profile", "user"],
    "useSignedUpload": true
  }
}
```

### 2. Complete Upload

```graphql
mutation CompleteUpload($id: ID!) {
  completeUpload(id: $id) {
    id
    filename
    status
    publicURL
    cdnURL
  }
}
```

### 3. Delete File

```graphql
mutation DeleteFile($id: ID!) {
  deleteFile(id: $id)
}
```

### 4. Update File Metadata

```graphql
mutation UpdateFileMetadata($id: ID!, $tags: [String!], $metadata: String) {
  updateFileMetadata(id: $id, tags: $tags, metadata: $metadata) {
    id
    tags
    metadata
  }
}
```

## 🌐 Workflow Upload Ảnh

### Phương pháp 1: Signed URL Upload (Recommended)

1. **Tạo signed upload URL:**
```graphql
mutation {
  uploadFile(input: {
    filename: "image.jpg"
    fileType: IMAGE
    mimeType: "image/jpeg"
    size: 1024000
    useSignedUpload: true
  }) {
    success
    uploadURL
    file { id }
  }
}
```

2. **Upload file trực tiếp lên R2 qua signed URL:**
```bash
curl -X PUT -T "image.jpg" "<uploadURL>"
```

3. **Complete upload:**
```graphql
mutation {
  completeUpload(id: "file-id") {
    id
    status
    publicURL
    cdnURL
  }
}
```

### Phương pháp 2: Direct Upload

Sử dụng REST endpoint `/api/cdn-service/upload` với multipart/form-data.

## 🔗 URL Types

- **PUBLIC**: URL công khai qua CDN (không hết hạn)
- **SIGNED**: URL có chữ ký số (có thời hạn, bảo mật cao)

## 📝 Examples với cURL

### Upload File:

```bash
curl -X POST http://localhost:8080/api/cdn-service/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "query": "mutation($input: UploadInput!) { uploadFile(input: $input) { success uploadURL file { id } } }",
    "variables": {
      "input": {
        "filename": "test.jpg",
        "fileType": "IMAGE",
        "mimeType": "image/jpeg",
        "size": 1024000,
        "useSignedUpload": true
      }
    }
  }'
```

### Get File:

```bash
curl -X POST http://localhost:8080/api/cdn-service/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "query": "query($id: ID!) { file(id: $id) { id filename publicURL cdnURL } }",
    "variables": { "id": "file-uuid" }
  }'
```

## 🛠️ Configuration

### R2 Storage:
```yaml
r2:
  account-id: "your-account-id"
  access-key-id: "your-access-key"
  secret-access-key: "your-secret-key"
  bucket-name: "your-bucket"
  region: "auto"
```

### CDN:
```yaml
cdn:
  enabled: true
  provider: "cloudflare"
  base-url: "https://your-cdn-domain.com"
  api-token: "your-api-token"
  zone-id: "your-zone-id"
```

## 🚨 Error Handling

GraphQL errors được trả về trong format:

```json
{
  "errors": [
    {
      "message": "File not found",
      "path": ["file"]
    }
  ]
}
```

## 🎯 Best Practices

1. **Sử dụng Signed URL Upload** cho file lớn
2. **Sử dụng CDN URLs** cho public access
3. **Sử dụng Signed URLs** cho private/secure access
4. **Implement proper error handling** cho GraphQL responses
5. **Use pagination** khi list files
6. **Add appropriate tags** để dễ quản lý

## 🔧 Development

- **GraphQL Playground**: http://localhost:8080/api/cdn-service/graphql/playground
- **Health Check**: http://localhost:8080/api/cdn-service/graphql/healthz
