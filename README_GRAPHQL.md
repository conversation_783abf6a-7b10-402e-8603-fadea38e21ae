# 🚀 XBIT CDN Service - GraphQL API Ready!

## ✅ Tình trạng hiện tại

**GraphQL API của bạn đã hoàn chỉnh và sẵn sàng sử dụng!** 🎉

### 🏗️ Kiến trúc hoạt động:
```
Other Services → xbit-cdn-service → Cloudflare R2 + CDN
```

### 📡 Endpoints sẵn sàng:
- **GraphQL API**: `http://localhost:8080/api/cdn-service/graphql`
- **GraphQL Playground**: `http://localhost:8080/api/cdn-service/graphql/playground`
- **Health Check**: `http://localhost:8080/api/cdn-service/graphql/healthz`
- **Authentication**: `http://localhost:8080/auth/login`

## 🎯 Tính năng chính

### ✅ Upload ảnh qua GraphQL:
- **Signed URL Upload** (recommended cho file lớn)
- **Direct Upload** (cho file nhỏ)
- **File validation** (type, size, extensions)
- **Metadata management** (tags, custom metadata)
- **Automatic CDN URL generation**

### ✅ Download ảnh qua GraphQL:
- **Public URLs** (qua CDN, không hết hạn)
- **Signed URLs** (có thời hạn, bảo mật)
- **Image optimization** (width, height, quality, format)

### ✅ Quản lý file:
- **List files** với filtering và pagination
- **Get file info** by ID
- **Update metadata** và tags
- **Delete files**
- **File status tracking** (UPLOADING, PROCESSING, READY, ERROR)

## 🚀 Quick Start

### 1. Khởi động service:
```bash
make run-local
```

### 2. Mở GraphQL Playground:
```
http://localhost:8080/api/cdn-service/graphql/playground
```

### 3. Đăng nhập để lấy token:
```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### 4. Test upload ảnh:
```graphql
mutation {
  uploadFile(input: {
    filename: "test.jpg"
    fileType: IMAGE
    mimeType: "image/jpeg"
    size: 1024000
    useSignedUpload: true
  }) {
    success
    uploadURL
    file {
      id
      filename
      publicURL
      cdnURL
    }
  }
}
```

## 📚 Documentation & Examples

### 📖 Chi tiết API:
- **[GRAPHQL_API_GUIDE.md](./GRAPHQL_API_GUIDE.md)** - Hướng dẫn đầy đủ GraphQL API

### 💻 Code Examples:
- **[JavaScript Client](./examples/javascript_client.js)** - Client cho web/Node.js
- **[Python Client](./examples/python_client.py)** - Client cho Python
- **[cURL Examples](./examples/curl_examples.sh)** - Examples với cURL

### 🧪 Test Examples:
```bash
# Test với cURL
./examples/curl_examples.sh

# Test với Python
python examples/python_client.py

# Test với JavaScript (Node.js)
node examples/javascript_client.js
```

## 🔧 Configuration

### R2 Storage (Cloudflare):
```yaml
r2:
  account-id: "your-account-id"
  access-key-id: "your-access-key"
  secret-access-key: "your-secret-key"
  bucket-name: "your-bucket"
  region: "auto"
```

### CDN (Cloudflare):
```yaml
cdn:
  enabled: true
  provider: "cloudflare"
  base-url: "https://your-cdn-domain.com"
  api-token: "your-api-token"
  zone-id: "your-zone-id"
```

## 🌟 Key Features

### 🔐 Authentication:
- JWT-based authentication
- Token expiration: 7 days
- Refresh token support

### 📁 File Support:
- **Images**: JPG, PNG, GIF, WebP
- **Videos**: MP4, AVI, MOV
- **Documents**: PDF, DOC, DOCX, TXT
- **Max size**: 100MB (configurable)

### 🚀 Performance:
- **CDN integration** cho fast delivery
- **Signed URLs** cho secure access
- **Image optimization** on-the-fly
- **Chunked upload** support

### 🛡️ Security:
- JWT authentication
- File type validation
- Size limits
- Signed URLs với expiration

## 🎨 GraphQL Schema Highlights

```graphql
type Query {
  file(id: ID!): FileMetadata
  files(filter: FileFilter, pagination: PaginationInput): FileListResponse!
  downloadURL(id: ID!, urlType: URLType, expiresIn: Int): DownloadResponse!
  health: String!
}

type Mutation {
  uploadFile(input: UploadInput!): UploadResponse!
  completeUpload(id: ID!): FileMetadata
  deleteFile(id: ID!): Boolean!
  updateFileMetadata(id: ID!, tags: [String!], metadata: String): FileMetadata
}
```

## 🔄 Workflow Upload Ảnh

### Recommended: Signed URL Upload
1. **Create upload request** → Get signed URL
2. **Upload file directly** to R2 via signed URL
3. **Complete upload** → File ready with CDN URLs

### Alternative: Direct Upload
1. **POST multipart/form-data** to `/api/cdn-service/upload`
2. **File processed** automatically
3. **Get file info** via GraphQL

## 🌐 Integration Examples

### Frontend (React/Vue/Angular):
```javascript
const client = new XbitCDNClient('http://localhost:8080');
await client.login('admin', 'admin123');
const result = await client.uploadFile(file, {tags: ['profile']});
```

### Backend (Python/Node.js/Go):
```python
client = XbitCDNClient("http://localhost:8080")
client.login("admin", "admin123")
result = client.upload_file("image.jpg", tags=["product"])
```

### Mobile (React Native/Flutter):
```javascript
// Same JavaScript client works for React Native
const uploadResult = await cdnClient.uploadFile(imageFile);
```

## 🚨 Error Handling

GraphQL errors format:
```json
{
  "errors": [
    {
      "message": "File not found",
      "path": ["file"]
    }
  ]
}
```

## 🎯 Best Practices

1. **Use Signed URL Upload** cho files > 1MB
2. **Use CDN URLs** cho public access
3. **Use Signed URLs** cho private/secure access
4. **Add meaningful tags** để dễ search/filter
5. **Implement retry logic** cho network errors
6. **Use pagination** khi list nhiều files

## 🔍 Monitoring & Health

- **Health endpoint**: `/api/cdn-service/graphql/healthz`
- **GraphQL introspection**: Enabled
- **Database connection**: Monitored
- **R2 connection**: Validated on startup

## 🎉 Kết luận

**GraphQL API của bạn đã hoàn chỉnh và production-ready!** 

Bạn có thể:
- ✅ Upload ảnh qua GraphQL (signed URL hoặc direct)
- ✅ Download ảnh với public/signed URLs
- ✅ Quản lý files với full CRUD operations
- ✅ Tích hợp với Cloudflare R2 + CDN
- ✅ Sử dụng từ bất kỳ client nào (web, mobile, backend)

**Next steps:**
1. Configure production R2 credentials
2. Setup production CDN domain
3. Deploy to production environment
4. Monitor performance and usage

Happy coding! 🚀
