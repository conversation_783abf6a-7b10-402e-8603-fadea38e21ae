# XBIT CDN Service Global Configuration

# jwt configuration
jwt:
  signing-key: "xbit-cdn-service"
  expires-time: "7d"
  buffer-time: "1d"
  issuer: "xbit-cdn-service"

# zap logger configuration
zap:
  level: info
  format: console
  prefix: '[xbit-cdn-service]'
  director: log
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  max-age: 0
  show-line: true
  log-in-console: true

# system configuration
system:
  env: "local"
  addr: "8080"
  db-type: pgsql
  oss-type: r2
  use-multipoint: false
  # Global route prefix
  router-prefix: "/api/cdn"
  graphql-prefix: "/api/cdn/graphql"

# postgresql database configuration
pgsql:
  path: "127.0.0.1"
  port: "5433"
  config: "sslmode=disable"
  db-name: "xbit_cdn"
  username: "postgres"
  password: "postgres"
  prefix: ''
  singular: false
  engine: ''
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ''
  log-zap: false

# Cloudflare R2 configuration
r2:
  account-id: ""
  access-key-id: ""
  secret-access-key: ""
  bucket-name: ""
  region: "auto"
  endpoint: ""

# CDN configuration
cdn:
  enabled: false
  provider: "cloudflare"
  base-url: ""
  api-token: ""
  zone-id: ""
  api-email: ""
  domain: ""
  cache-ttl: 3600
  image-optimize: true

# Upload configuration
upload:
  max-file-size: *********  # 100MB
  allowed-extensions: [".jpg", ".jpeg", ".png", ".gif", ".webp", ".pdf", ".mp4"]
  signed-url-expiry: 3600  # 1 hour
  chunk-size: 5242880  # 5MB

# upload configuration
upload:
  max-file-size: *********  # 100MB
  allowed-extensions:
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".gif"
    - ".webp"
    - ".svg"
    - ".pdf"
    - ".doc"
    - ".docx"
    - ".txt"
    - ".mp4"
    - ".avi"
    - ".mov"
  signed-url-expiry: 3600  # 1 hour
  chunk-size: 5242880  # 5MB

# cors configuration
cors:
  mode: strict-whitelist
  whitelist:
    - allow-origin: example1.com
      allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id
      allow-methods: POST, GET, PUT, DELETE, OPTIONS
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true
    - allow-origin: example2.com
      allow-headers: content-type
      allow-methods: GET, POST, PUT, DELETE, OPTIONS
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true

# local file storage configuration
local:
  path: uploads/file
  store-path: uploads/file
