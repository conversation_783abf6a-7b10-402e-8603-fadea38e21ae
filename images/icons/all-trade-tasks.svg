<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<path opacity="0.2" d="M40 20C40 31.0457 31.0457 40 20 40C8.9543 40 0 31.0457 0 20C0 8.9543 8.9543 0 20 0C31.0457 0 40 8.9543 40 20Z" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<foreignObject x="3.62875" y="6.62899" width="30.7425" height="28.742"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.64px);clip-path:url(#bgblur_0_45761_7540_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_45761_7540)" data-figma-bg-blur-radius="5.28312">
<path d="M9 16C9 13.7909 10.7909 12 13 12H25C27.2091 12 29 13.7909 29 16V26C29 28.2091 27.2091 30 25 30H13C10.7909 30 9 28.2091 9 26V21V16Z" fill="#C78EFF" fill-opacity="0.4" style="fill:#C78EFF;fill:color(display-p3 0.7791 0.5581 1.0000);fill-opacity:0.4;"/>
<path d="M9 16C9 13.7909 10.7909 12 13 12H25C27.2091 12 29 13.7909 29 16V26C29 28.2091 27.2091 30 25 30H13C10.7909 30 9 28.2091 9 26V21V16Z" stroke="url(#paint0_linear_45761_7540)" style="" stroke-width="0.176104"/>
</g>
<path d="M19 16C19 12.6863 21.6863 10 25 10C28.3137 10 31 12.6863 31 16C31 19.3137 28.3137 22 25 22C21.6863 22 19 19.3137 19 16Z" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<path d="M25.207 18.708L24.9629 18.6963C24.833 18.6898 24.7011 18.6759 24.5732 18.6621H24.5664C24.4056 18.6493 24.0403 18.6163 23.7207 18.4629L23.5879 18.3896C23.4206 18.2807 23.3476 18.1777 23.3066 18.04C23.271 17.92 23.257 17.766 23.2568 17.5439H24.5439V17.6396C24.5434 17.6505 24.5423 17.7044 24.5449 17.7451C24.5484 17.7995 24.5595 17.8759 24.5928 17.9551V17.9561C24.5935 17.9581 24.5949 17.9599 24.5957 17.9619C24.5973 17.9656 24.5979 17.9699 24.5996 17.9736L24.6006 17.9727C24.6463 18.0825 24.7302 18.182 24.8643 18.25C24.9972 18.3173 25.1673 18.3486 25.376 18.3486C25.7122 18.3486 25.9675 18.2833 26.1162 18.1025C26.2588 17.9289 26.2393 17.7083 26.2393 17.6543C26.2393 17.64 26.2384 17.5851 26.2295 17.5186C26.2212 17.457 26.2028 17.3594 26.1514 17.2705L26.1504 17.2686L26.0723 17.166C25.9843 17.0722 25.8686 17.0029 25.7607 16.9453C25.6095 16.8646 25.4107 16.7775 25.1953 16.6787L25.1924 16.6768C24.8745 16.5361 24.5748 16.3923 24.2598 16.248L24.2578 16.2471L24.0049 16.126C23.8878 16.0662 23.7543 15.9927 23.6553 15.9209L23.6543 15.9199L23.582 15.8633C23.5165 15.8069 23.4709 15.75 23.4375 15.6816C23.3914 15.5874 23.3574 15.4482 23.3574 15.2178C23.3574 14.8805 23.4001 14.6079 23.6621 14.4033L23.665 14.4014C23.8025 14.2914 23.9902 14.2165 24.2207 14.167C24.4509 14.1176 24.7053 14.097 24.9639 14.083L25.207 14.0703V13.2568H25.5752V14.0703L25.8184 14.083C26.0817 14.0972 26.3406 14.1129 26.5752 14.1592C26.8105 14.2056 26.9942 14.2778 27.1211 14.3877L27.125 14.3916C27.252 14.497 27.3176 14.6451 27.3477 14.832C27.3659 14.9457 27.3694 15.062 27.3682 15.1797H26.1543V15.0693C26.1543 15.0183 26.1545 14.97 26.1504 14.9268L26.1211 14.7891C26.0568 14.5774 25.8754 14.4883 25.7422 14.4512C25.6076 14.4137 25.4575 14.4092 25.3438 14.4092C25.0629 14.4092 24.8342 14.4882 24.71 14.6934C24.6551 14.7841 24.6347 14.8791 24.626 14.9561C24.6174 15.0311 24.6182 15.1086 24.6182 15.1611C24.6182 15.2741 24.6325 15.378 24.6689 15.4688L24.7129 15.5547L24.7197 15.5645C24.7712 15.6366 24.8425 15.6925 24.9062 15.7344C24.9737 15.7786 25.0535 15.8203 25.1377 15.8613C25.3055 15.943 25.5189 16.0311 25.7441 16.125C26.0849 16.2736 26.4085 16.4119 26.7324 16.5645V16.5654C27.0258 16.7047 27.158 16.7813 27.2549 16.8516L27.2559 16.8525C27.3543 16.9236 27.4196 16.9939 27.4639 17.085C27.5095 17.1789 27.543 17.3161 27.543 17.5342C27.543 17.7241 27.5292 17.8823 27.4756 18.0195C27.4252 18.1482 27.3328 18.2736 27.1455 18.3867L27.1445 18.3877C26.7906 18.6049 26.3632 18.6541 25.8125 18.6973L25.5752 18.7158V19.5996H25.207V18.708Z" fill="white" stroke="white" style="fill:white;fill-opacity:1;stroke:white;stroke-opacity:1;" stroke-width="0.514286"/>
<foreignObject x="3.62875" y="8.62899" width="31.7425" height="26.742"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.64px);clip-path:url(#bgblur_1_45761_7540_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_i_45761_7540)" data-figma-bg-blur-radius="5.28312">
<path d="M9 18C9 15.7909 10.7909 14 13 14H26C28.2091 14 30 15.7909 30 18V26C30 28.2091 28.2091 30 26 30H13C10.7909 30 9 28.2091 9 26V22V18Z" fill="#C78EFF" fill-opacity="0.4" style="fill:#C78EFF;fill:color(display-p3 0.7791 0.5581 1.0000);fill-opacity:0.4;"/>
<path d="M9 18C9 15.7909 10.7909 14 13 14H26C28.2091 14 30 15.7909 30 18V26C30 28.2091 28.2091 30 26 30H13C10.7909 30 9 28.2091 9 26V22V18Z" stroke="url(#paint1_linear_45761_7540)" style="" stroke-width="0.176104"/>
</g>
<path d="M18.5752 24.6602L18.29 24.6455C18.1385 24.638 17.9851 24.6225 17.8359 24.6064L17.8281 24.6055L17.6152 24.584C17.4011 24.5569 17.108 24.5007 16.8418 24.373L16.6855 24.2881C16.4905 24.1611 16.4052 24.0413 16.3574 23.8809C16.3158 23.7407 16.2999 23.5604 16.2998 23.3008H17.8018V23.5361C17.8058 23.5996 17.8186 23.6889 17.8574 23.7812V23.7822C17.8584 23.7849 17.8603 23.7873 17.8613 23.79C17.8631 23.7942 17.8643 23.7986 17.8662 23.8027L17.8672 23.8018C17.9206 23.9299 18.0184 24.0467 18.1748 24.126C18.3299 24.2045 18.5281 24.2402 18.7715 24.2402C19.1639 24.2402 19.4621 24.1641 19.6357 23.9531C19.8024 23.7502 19.7783 23.4921 19.7783 23.4297C19.7783 23.4131 19.7789 23.3499 19.7686 23.2725C19.7589 23.2007 19.7369 23.0864 19.6768 22.9824L19.6748 22.9805L19.6328 22.918C19.527 22.7789 19.3684 22.6809 19.2217 22.6025C19.0452 22.5083 18.8129 22.4073 18.5615 22.292L18.5576 22.29C18.1866 22.1259 17.8373 21.9574 17.4697 21.7891L17.4668 21.7881L17.1729 21.6475C17.0359 21.5776 16.8794 21.4912 16.7637 21.4072V21.4062L16.6787 21.3408C16.6022 21.275 16.5488 21.2086 16.5098 21.1289C16.456 21.0189 16.417 20.8559 16.417 20.5869C16.417 20.1937 16.4671 19.8763 16.7725 19.6377L16.7754 19.6348C16.9358 19.5063 17.1555 19.4191 17.4248 19.3613C17.6932 19.3038 17.9895 19.2799 18.291 19.2637L18.5752 19.248V18.2998H19.0049V19.248L19.2881 19.2637C19.5952 19.2802 19.8973 19.2976 20.1709 19.3516C20.4454 19.4057 20.6596 19.491 20.8076 19.6191L20.8125 19.623C20.9607 19.7461 21.0372 19.9196 21.0723 20.1377C21.0935 20.2699 21.0971 20.4051 21.0957 20.542H19.6797V20.4141C19.6797 20.3543 19.6796 20.2977 19.6748 20.2471C19.6719 20.2169 19.6678 20.1898 19.6621 20.1641L19.6416 20.0879C19.5668 19.8405 19.3547 19.7367 19.1992 19.6934C19.0421 19.6496 18.8671 19.6445 18.7344 19.6445C18.4067 19.6445 18.1401 19.7362 17.9951 19.9756C17.931 20.0815 17.9077 20.1924 17.8975 20.2822C17.8875 20.3698 17.8887 20.4601 17.8887 20.5215C17.8887 20.653 17.9039 20.7742 17.9463 20.8799L17.998 20.9805L18.0059 20.9912C18.066 21.0757 18.1501 21.1406 18.2246 21.1895C18.3033 21.2411 18.3958 21.291 18.4941 21.3389C18.6898 21.4341 18.9386 21.536 19.2012 21.6455C19.599 21.819 19.9763 21.9811 20.3545 22.1592V22.1602C20.6117 22.2822 20.7631 22.3623 20.8701 22.4297L20.9648 22.4941C21.0798 22.577 21.1564 22.6594 21.208 22.7656C21.2612 22.8752 21.2998 23.0356 21.2998 23.29C21.2998 23.5114 21.2841 23.6955 21.2217 23.8555C21.1629 24.0057 21.0556 24.1531 20.8369 24.2852L20.835 24.2861C20.422 24.5395 19.9236 24.5961 19.2812 24.6465L19.0049 24.668V25.7002H18.5752V24.6602Z" fill="white" stroke="white" style="fill:white;fill-opacity:1;stroke:white;stroke-opacity:1;" stroke-width="0.6"/>
<path d="M25 22.5C25 21.6716 25.6716 21 26.5 21H30V24H26.5C25.6716 24 25 23.3284 25 22.5Z" fill="white" style="fill:white;fill-opacity:1;"/>
<circle cx="26.5" cy="22.5" r="0.75" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<defs>
<filter id="filter0_i_45761_7540" x="3.62875" y="6.62899" width="30.7425" height="28.742" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.528312" dy="-0.528312"/>
<feGaussianBlur stdDeviation="1.58493"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.704518 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_45761_7540"/>
</filter>
<clipPath id="bgblur_0_45761_7540_clip_path" transform="translate(-3.62875 -6.62899)"><path d="M9 16C9 13.7909 10.7909 12 13 12H25C27.2091 12 29 13.7909 29 16V26C29 28.2091 27.2091 30 25 30H13C10.7909 30 9 28.2091 9 26V21V16Z"/>
</clipPath><filter id="filter1_i_45761_7540" x="3.62875" y="8.62899" width="31.7425" height="26.742" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.528312" dy="-0.528312"/>
<feGaussianBlur stdDeviation="1.58493"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.704518 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_45761_7540"/>
</filter>
<clipPath id="bgblur_1_45761_7540_clip_path" transform="translate(-3.62875 -8.62899)"><path d="M9 18C9 15.7909 10.7909 14 13 14H26C28.2091 14 30 15.7909 30 18V26C30 28.2091 28.2091 30 26 30H13C10.7909 30 9 28.2091 9 26V22V18Z"/>
</clipPath><linearGradient id="paint0_linear_45761_7540" x1="15.5004" y1="12" x2="18.0739" y2="30.7844" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4" style="stop-color:white;stop-opacity:0.4;"/>
<stop offset="1" stop-color="#F7D7FF" stop-opacity="0.5" style="stop-color:#F7D7FF;stop-color:color(display-p3 0.9683 0.8416 1.0000);stop-opacity:0.5;"/>
</linearGradient>
<linearGradient id="paint1_linear_45761_7540" x1="15.8254" y1="14" x2="17.7721" y2="30.7849" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4" style="stop-color:white;stop-opacity:0.4;"/>
<stop offset="1" stop-color="#F7D7FF" stop-opacity="0.5" style="stop-color:#F7D7FF;stop-color:color(display-p3 0.9683 0.8416 1.0000);stop-opacity:0.5;"/>
</linearGradient>
</defs>
</svg>
