<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle opacity="0.2" cx="20" cy="20" r="20" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<path d="M10 15C10 13.3431 11.3431 12 13 12H27C28.6569 12 30 13.3431 30 15V21C30 21.5523 29.5523 22 29 22H11C10.4477 22 10 21.5523 10 21V15Z" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<foreignObject x="3.8999" y="9.8999" width="32.2002" height="28.2002"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3px);clip-path:url(#bgblur_0_45783_841649_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_45783_841649)" data-figma-bg-blur-radius="6">
<rect x="10" y="16" width="20" height="16" rx="3" fill="#C78EFF" fill-opacity="0.4" style="fill:#C78EFF;fill:color(display-p3 0.7791 0.5581 1.0000);fill-opacity:0.4;"/>
<rect x="10" y="16" width="20" height="16" rx="3" stroke="url(#paint0_linear_45783_841649)" style="" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_d_45783_841649)">
<path d="M16.74 22.028L16.927 22.017C17.3377 22.017 17.7227 22.1197 18.082 22.325C18.4487 22.523 18.742 22.8127 18.962 23.194C19.182 23.5753 19.292 24.0227 19.292 24.536C19.292 25.0713 19.1673 25.548 18.918 25.966C18.676 26.3767 18.335 26.6957 17.895 26.923C17.455 27.1503 16.949 27.264 16.377 27.264C15.695 27.264 15.1047 27.1063 14.606 26.791C14.1147 26.4683 13.7443 26.0393 13.495 25.504L14.683 24.69C14.8737 25.086 15.1047 25.3867 15.376 25.592C15.6547 25.79 15.992 25.889 16.388 25.889C16.7987 25.889 17.1323 25.7643 17.389 25.515C17.6457 25.2657 17.774 24.9393 17.774 24.536C17.774 24.118 17.6457 23.788 17.389 23.546C17.1397 23.2967 16.7987 23.1647 16.366 23.15C16.08 23.15 15.7903 23.238 15.497 23.414L14.672 22.336L16.872 20.103H13.792V18.695H18.863V19.938L16.718 21.973L16.74 22.028ZM23.4298 27.264C22.4471 27.264 21.6918 26.8937 21.1638 26.153C20.6358 25.4123 20.3718 24.3307 20.3718 22.908C20.3718 21.478 20.6358 20.3927 21.1638 19.652C21.6918 18.9113 22.4471 18.541 23.4298 18.541C24.4198 18.541 25.1788 18.9113 25.7068 19.652C26.2348 20.3927 26.4988 21.478 26.4988 22.908C26.4988 24.3307 26.2348 25.4123 25.7068 26.153C25.1788 26.8937 24.4198 27.264 23.4298 27.264ZM23.4298 25.856C23.9138 25.856 24.2878 25.6067 24.5518 25.108C24.8158 24.6093 24.9478 23.876 24.9478 22.908C24.9478 21.9327 24.8158 21.1957 24.5518 20.697C24.2878 20.1983 23.9138 19.949 23.4298 19.949C22.9458 19.949 22.5718 20.1983 22.3078 20.697C22.0511 21.1957 21.9228 21.9327 21.9228 22.908C21.9228 23.8833 22.0511 24.6203 22.3078 25.119C22.5718 25.6103 22.9458 25.856 23.4298 25.856Z" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<g filter="url(#filter2_d_45783_841649)">
<rect x="16" y="10" width="1.6" height="4" rx="0.8" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<g filter="url(#filter3_d_45783_841649)">
<rect x="23" y="10" width="1.6" height="4" rx="0.8" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<defs>
<filter id="filter0_i_45783_841649" x="3.8999" y="9.8999" width="32.2002" height="28.2002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.6" dy="-0.6"/>
<feGaussianBlur stdDeviation="1.8"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.704518 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_45783_841649"/>
</filter>
<clipPath id="bgblur_0_45783_841649_clip_path" transform="translate(-3.8999 -9.8999)"><rect x="10" y="16" width="20" height="16" rx="3"/>
</clipPath><filter id="filter1_d_45783_841649" x="11.4951" y="17.541" width="17.0037" height="12.7231" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.549308 0 0 0 0 0.124589 0 0 0 0 0.974027 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45783_841649"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45783_841649" result="shape"/>
</filter>
<filter id="filter2_d_45783_841649" x="16" y="10" width="1.6001" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0749787 0 0 0 0 0.0229745 0 0 0 0 0.24585 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45783_841649"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45783_841649" result="shape"/>
</filter>
<filter id="filter3_d_45783_841649" x="23" y="10" width="1.6001" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0749787 0 0 0 0 0.0229745 0 0 0 0 0.24585 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45783_841649"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45783_841649" result="shape"/>
</filter>
<linearGradient id="paint0_linear_45783_841649" x1="16.5" y1="16" x2="18.5413" y2="32.7621" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4" style="stop-color:white;stop-opacity:0.4;"/>
<stop offset="1" stop-color="#F7D7FF" stop-opacity="0.5" style="stop-color:#F7D7FF;stop-color:color(display-p3 0.9683 0.8416 1.0000);stop-opacity:0.5;"/>
</linearGradient>
</defs>
</svg>
