<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle opacity="0.2" cx="20" cy="20" r="20" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<path d="M14.1256 13C11.2939 13 8.99829 15.2956 8.99829 18.1273C8.99829 23.2546 15.0578 27.9157 18.3206 29C21.5834 27.9157 27.6429 23.2546 27.6429 18.1273C27.6429 15.2956 25.3474 13 22.5157 13C20.7816 13 19.2485 13.8609 18.3206 15.1785C17.3927 13.8609 15.8597 13 14.1256 13Z" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<foreignObject x="12.627" y="12.629" width="25.7425" height="24.742"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.64px);clip-path:url(#bgblur_0_45761_7172_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_45761_7172)" data-figma-bg-blur-radius="5.28312">
<rect x="17.9983" y="18" width="15" height="14" rx="4" fill="#C78EFF" fill-opacity="0.4" style="fill:#C78EFF;fill:color(display-p3 0.7791 0.5581 1.0000);fill-opacity:0.4;"/>
<rect x="17.9983" y="18" width="15" height="14" rx="4" stroke="url(#paint0_linear_45761_7172)" style="" stroke-width="0.176104"/>
</g>
<g filter="url(#filter1_d_45761_7172)">
<path d="M24.6483 23C23.737 23 22.9983 23.7174 22.9983 24.6023C22.9983 26.2045 24.9483 27.6612 25.9983 28C27.0483 27.6612 28.9983 26.2045 28.9983 24.6023C28.9983 23.7174 28.2596 23 27.3483 23C26.7902 23 26.2969 23.269 25.9983 23.6808C25.6997 23.269 25.2063 23 24.6483 23Z" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<defs>
<filter id="filter0_i_45761_7172" x="12.627" y="12.629" width="25.7425" height="24.742" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.528312" dy="-0.528312"/>
<feGaussianBlur stdDeviation="1.58493"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.704518 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_45761_7172"/>
</filter>
<clipPath id="bgblur_0_45761_7172_clip_path" transform="translate(-12.627 -12.629)"><rect x="17.9983" y="18" width="15" height="14" rx="4"/>
</clipPath><filter id="filter1_d_45761_7172" x="18.9983" y="20" width="14" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.6 0 0 0 0 0.270588 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45761_7172"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45761_7172" result="shape"/>
</filter>
<linearGradient id="paint0_linear_45761_7172" x1="22.8733" y1="18" x2="24.9461" y2="32.5898" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4" style="stop-color:white;stop-opacity:0.4;"/>
<stop offset="1" stop-color="#F7D7FF" stop-opacity="0.5" style="stop-color:#F7D7FF;stop-color:color(display-p3 0.9683 0.8416 1.0000);stop-opacity:0.5;"/>
</linearGradient>
</defs>
</svg>
