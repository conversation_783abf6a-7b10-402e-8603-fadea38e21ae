<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle opacity="0.2" cx="20" cy="20" r="20" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<path d="M17.002 14.5221C17.002 12.5769 18.5788 11 20.524 11H27.4799C29.4251 11 31.002 12.5769 31.002 14.5221V21.4779C31.002 23.4231 29.4251 25 27.4799 25H20.524C18.5788 25 17.002 23.4231 17.002 21.4779V14.5221Z" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<foreignObject x="3.6307" y="8.62899" width="29.7425" height="28.742"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.64px);clip-path:url(#bgblur_0_45761_7430_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_45761_7430)" data-figma-bg-blur-radius="5.28312">
<rect x="9.00195" y="14" width="19" height="18" rx="4" fill="#C78EFF" fill-opacity="0.4" style="fill:#C78EFF;fill:color(display-p3 0.7791 0.5581 1.0000);fill-opacity:0.4;"/>
<rect x="9.00195" y="14" width="19" height="18" rx="4" stroke="url(#paint0_linear_45761_7430)" style="" stroke-width="0.176104"/>
</g>
<g filter="url(#filter1_d_45761_7430)">
<path d="M12.501 21.375L14.501 19.375M14.501 19.375L16.501 21.375M14.501 19.375V25.375C14.501 26.4796 15.3964 27.375 16.501 27.375H19.501" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="1.3125"/>
<path d="M25.0005 25.375L23.0005 27.375M23.0005 27.375L21.0005 25.375M23.0005 27.375V21.375C23.0005 20.2704 22.1051 19.375 21.0005 19.375H18.0005" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="1.3125"/>
</g>
<defs>
<filter id="filter0_i_45761_7430" x="3.6307" y="8.62899" width="29.7425" height="28.742" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.528312" dy="-0.528312"/>
<feGaussianBlur stdDeviation="1.58493"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.704518 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_45761_7430"/>
</filter>
<clipPath id="bgblur_0_45761_7430_clip_path" transform="translate(-3.6307 -8.62899)"><rect x="9.00195" y="14" width="19" height="18" rx="4"/>
</clipPath><filter id="filter1_d_45761_7430" x="9.03687" y="16.4468" width="19.4277" height="15.8564" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.6 0 0 0 0 0.270588 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45761_7430"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45761_7430" result="shape"/>
</filter>
<linearGradient id="paint0_linear_45761_7430" x1="15.177" y1="14" x2="17.8805" y2="32.7471" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4" style="stop-color:white;stop-opacity:0.4;"/>
<stop offset="1" stop-color="#F7D7FF" stop-opacity="0.5" style="stop-color:#F7D7FF;stop-color:color(display-p3 0.9683 0.8416 1.0000);stop-opacity:0.5;"/>
</linearGradient>
</defs>
</svg>
