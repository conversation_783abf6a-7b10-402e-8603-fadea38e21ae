<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle opacity="0.2" cx="20" cy="20" r="20" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<path d="M10 15C10 13.3431 11.3431 12 13 12H27C28.6569 12 30 13.3431 30 15V21C30 21.5523 29.5523 22 29 22H11C10.4477 22 10 21.5523 10 21V15Z" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<foreignObject x="3.8999" y="9.8999" width="32.2002" height="28.2002"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3px);clip-path:url(#bgblur_0_45783_841659_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_45783_841659)" data-figma-bg-blur-radius="6">
<rect x="10" y="16" width="20" height="16" rx="3" fill="#C78EFF" fill-opacity="0.4" style="fill:#C78EFF;fill:color(display-p3 0.7791 0.5581 1.0000);fill-opacity:0.4;"/>
<rect x="10" y="16" width="20" height="16" rx="3" stroke="url(#paint0_linear_45783_841659)" style="" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_d_45783_841659)">
<path d="M18.748 28.12H16.924L20.38 20.512H16.324V18.94H22.348V20.032L18.748 28.12Z" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<g filter="url(#filter2_d_45783_841659)">
<rect x="16" y="10" width="1.6" height="4" rx="0.8" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<g filter="url(#filter3_d_45783_841659)">
<rect x="23" y="10" width="1.6" height="4" rx="0.8" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<defs>
<filter id="filter0_i_45783_841659" x="3.8999" y="9.8999" width="32.2002" height="28.2002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.6" dy="-0.6"/>
<feGaussianBlur stdDeviation="1.8"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.704518 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_45783_841659"/>
</filter>
<clipPath id="bgblur_0_45783_841659_clip_path" transform="translate(-3.8999 -9.8999)"><rect x="10" y="16" width="20" height="16" rx="3"/>
</clipPath><filter id="filter1_d_45783_841659" x="14.324" y="17.9399" width="10.0239" height="13.1802" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.549308 0 0 0 0 0.124589 0 0 0 0 0.974027 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45783_841659"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45783_841659" result="shape"/>
</filter>
<filter id="filter2_d_45783_841659" x="16" y="10" width="1.6001" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0749787 0 0 0 0 0.0229745 0 0 0 0 0.24585 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45783_841659"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45783_841659" result="shape"/>
</filter>
<filter id="filter3_d_45783_841659" x="23" y="10" width="1.6001" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0749787 0 0 0 0 0.0229745 0 0 0 0 0.24585 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45783_841659"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45783_841659" result="shape"/>
</filter>
<linearGradient id="paint0_linear_45783_841659" x1="16.5" y1="16" x2="18.5413" y2="32.7621" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4" style="stop-color:white;stop-opacity:0.4;"/>
<stop offset="1" stop-color="#F7D7FF" stop-opacity="0.5" style="stop-color:#F7D7FF;stop-color:color(display-p3 0.9683 0.8416 1.0000);stop-opacity:0.5;"/>
</linearGradient>
</defs>
</svg>
