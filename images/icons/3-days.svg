<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle opacity="0.2" cx="20" cy="20" r="20" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<path d="M10 15C10 13.3431 11.3431 12 13 12H27C28.6569 12 30 13.3431 30 15V21C30 21.5523 29.5523 22 29 22H11C10.4477 22 10 21.5523 10 21V15Z" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<foreignObject x="3.8999" y="9.8999" width="32.2002" height="28.2002"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3px);clip-path:url(#bgblur_0_41055_1268887_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_41055_1268887)" data-figma-bg-blur-radius="6">
<rect x="10" y="16" width="20" height="16" rx="3" fill="#C78EFF" fill-opacity="0.4" style="fill:#C78EFF;fill:color(display-p3 0.7791 0.5581 1.0000);fill-opacity:0.4;"/>
<rect x="10" y="16" width="20" height="16" rx="3" stroke="url(#paint0_linear_41055_1268887)" style="" stroke-width="0.2"/>
</g>
<g filter="url(#filter1_d_41055_1268887)">
<path d="M20.08 22.576L20.284 22.564C20.732 22.564 21.152 22.676 21.544 22.9C21.944 23.116 22.264 23.432 22.504 23.848C22.744 24.264 22.864 24.752 22.864 25.312C22.864 25.896 22.728 26.416 22.456 26.872C22.192 27.32 21.82 27.668 21.34 27.916C20.86 28.164 20.308 28.288 19.684 28.288C18.94 28.288 18.296 28.116 17.752 27.772C17.216 27.42 16.812 26.952 16.54 26.368L17.836 25.48C18.044 25.912 18.296 26.24 18.592 26.464C18.896 26.68 19.264 26.788 19.696 26.788C20.144 26.788 20.508 26.652 20.788 26.38C21.068 26.108 21.208 25.752 21.208 25.312C21.208 24.856 21.068 24.496 20.788 24.232C20.516 23.96 20.144 23.816 19.672 23.8C19.36 23.8 19.044 23.896 18.724 24.088L17.824 22.912L20.224 20.476H16.864V18.94H22.396V20.296L20.056 22.516L20.08 22.576Z" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<g filter="url(#filter2_d_41055_1268887)">
<rect x="16" y="10" width="1.6" height="4" rx="0.8" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<g filter="url(#filter3_d_41055_1268887)">
<rect x="23" y="10" width="1.6" height="4" rx="0.8" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<defs>
<filter id="filter0_i_41055_1268887" x="3.8999" y="9.8999" width="32.2002" height="28.2002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.6" dy="-0.6"/>
<feGaussianBlur stdDeviation="1.8"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.704518 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_41055_1268887"/>
</filter>
<clipPath id="bgblur_0_41055_1268887_clip_path" transform="translate(-3.8999 -9.8999)"><rect x="10" y="16" width="20" height="16" rx="3"/>
</clipPath><filter id="filter1_d_41055_1268887" x="14.54" y="17.9399" width="10.324" height="13.3481" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.549308 0 0 0 0 0.124589 0 0 0 0 0.974027 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_41055_1268887"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_41055_1268887" result="shape"/>
</filter>
<filter id="filter2_d_41055_1268887" x="16" y="10" width="1.6001" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0749787 0 0 0 0 0.0229745 0 0 0 0 0.24585 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_41055_1268887"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_41055_1268887" result="shape"/>
</filter>
<filter id="filter3_d_41055_1268887" x="23" y="10" width="1.6001" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0749787 0 0 0 0 0.0229745 0 0 0 0 0.24585 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_41055_1268887"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_41055_1268887" result="shape"/>
</filter>
<linearGradient id="paint0_linear_41055_1268887" x1="16.5" y1="16" x2="18.5413" y2="32.7621" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4" style="stop-color:white;stop-opacity:0.4;"/>
<stop offset="1" stop-color="#F7D7FF" stop-opacity="0.5" style="stop-color:#F7D7FF;stop-color:color(display-p3 0.9683 0.8416 1.0000);stop-opacity:0.5;"/>
</linearGradient>
</defs>
</svg>
