<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle opacity="0.2" cx="20" cy="20" r="20" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<rect x="7" y="13.124" width="18.2714" height="18.4413" rx="4.34656" transform="rotate(-13.0445 7 13.124)" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<foreignObject x="6.70503" y="6.70479" width="30.5899" height="30.5904"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.85px);clip-path:url(#bgblur_0_45761_9800_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_45761_9800)" data-figma-bg-blur-radius="5.7">
<rect x="12.5" y="12.5" width="19" height="19" rx="3.8" fill="#C78EFF" fill-opacity="0.4" style="fill:#C78EFF;fill:color(display-p3 0.7791 0.5581 1.0000);fill-opacity:0.4;"/>
<rect x="12.5" y="12.5" width="19" height="19" rx="3.8" stroke="url(#paint0_linear_45761_9800)" style="" stroke-width="0.19"/>
</g>
<g filter="url(#filter1_d_45761_9800)">
<path d="M28.262 18.0922C28.4176 18.1787 28.5361 18.3194 28.595 18.4875C28.6538 18.6555 28.6489 18.8394 28.5812 19.0041L28.5449 19.0812L23.9087 24.6264C23.8609 24.7124 23.7962 24.7878 23.7185 24.848C23.6407 24.9083 23.5516 24.9522 23.4564 24.977C23.3612 25.0018 23.262 25.007 23.1647 24.9924C23.0675 24.9778 22.9742 24.9436 22.8905 24.8918L22.8185 24.8409L19.6892 22.3407L17.2413 24.7871C17.1161 24.9123 16.9495 24.9875 16.7727 24.9987C16.596 25.0098 16.4213 24.956 16.2813 24.8475L16.213 24.7871C16.0878 24.6619 16.0125 24.4953 16.0014 24.3185C15.9903 24.1418 16.0441 23.9671 16.1526 23.8272L16.213 23.7588L19.122 20.8527C19.2373 20.7374 19.3881 20.6643 19.5501 20.645C19.7121 20.6258 19.8758 20.6615 20.015 20.7466L20.0899 20.7989L23.0542 23.1675L27.2729 18.3743C27.3193 18.2909 27.3817 18.2173 27.4566 18.158C27.5314 18.0986 27.6172 18.0546 27.709 18.0284C27.8009 18.0022 27.897 17.9943 27.9918 18.0053C28.0867 18.0162 28.1785 18.0457 28.262 18.0922Z" fill="white" style="fill:white;fill-opacity:1;"/>
</g>
<defs>
<filter id="filter0_i_45761_9800" x="6.70503" y="6.70479" width="30.5899" height="30.5904" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.57" dy="-0.57"/>
<feGaussianBlur stdDeviation="1.71"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.704518 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_45761_9800"/>
</filter>
<clipPath id="bgblur_0_45761_9800_clip_path" transform="translate(-6.70503 -6.70479)"><rect x="12.5" y="12.5" width="19" height="19" rx="3.8"/>
</clipPath><filter id="filter1_d_45761_9800" x="14" y="17.0005" width="16.6357" height="11" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.549308 0 0 0 0 0.124589 0 0 0 0 0.974027 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45761_9800"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45761_9800" result="shape"/>
</filter>
<linearGradient id="paint0_linear_45761_9800" x1="18.675" y1="12.5" x2="21.6803" y2="32.2427" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4" style="stop-color:white;stop-opacity:0.4;"/>
<stop offset="1" stop-color="#F7D7FF" stop-opacity="0.5" style="stop-color:#F7D7FF;stop-color:color(display-p3 0.9683 0.8416 1.0000);stop-opacity:0.5;"/>
</linearGradient>
</defs>
</svg>
