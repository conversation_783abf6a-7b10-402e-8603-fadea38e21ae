<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle opacity="0.2" cx="20" cy="20" r="20" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<path d="M27.0303 32.2502C28.5137 32.2502 29.3396 30.5356 28.4151 29.3757L21.9318 21.2412C21.2156 20.3426 19.8465 20.3536 19.1447 21.2635L12.8714 29.3979C11.9736 30.5622 12.8035 32.2502 14.2737 32.2502L27.0303 32.2502Z" fill="#843BEA" style="fill:#843BEA;fill:color(display-p3 0.5176 0.2314 0.9176);fill-opacity:1;"/>
<foreignObject x="6.79611" y="2.79294" width="28.4136" height="28.4141"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.81px);clip-path:url(#bgblur_0_45761_3794_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_45761_3794)" data-figma-bg-blur-radius="5.61331">
<rect x="12.5029" y="8.5" width="17" height="17" rx="8.5" fill="#C78EFF" fill-opacity="0.4" style="fill:#C78EFF;fill:color(display-p3 0.7791 0.5581 1.0000);fill-opacity:0.4;"/>
<rect x="12.5029" y="8.5" width="17" height="17" rx="8.5" stroke="url(#paint0_linear_45761_3794)" style="" stroke-width="0.18711"/>
</g>
<path d="M25.5002 27.5H27.5002M29.5002 27.5H27.5002M27.5002 27.5V25.5M27.5002 27.5V29.5" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="1.33333" stroke-linecap="round"/>
<g filter="url(#filter1_d_45761_3794)">
<path d="M24 18.5C24 20.1569 22.6569 21.5 21 21.5C19.3431 21.5 18 20.1569 18 18.5" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="1.5" stroke-linecap="round"/>
</g>
<defs>
<filter id="filter0_i_45761_3794" x="6.79611" y="2.79294" width="28.4136" height="28.4141" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.561331" dy="-0.561331"/>
<feGaussianBlur stdDeviation="1.68399"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.704518 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_45761_3794"/>
</filter>
<clipPath id="bgblur_0_45761_3794_clip_path" transform="translate(-6.79611 -2.79294)"><rect x="12.5029" y="8.5" width="17" height="17" rx="8.5"/>
</clipPath><filter id="filter1_d_45761_3794" x="13.25" y="14.75" width="15.5" height="12.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.6 0 0 0 0 0.270588 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_45761_3794"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_45761_3794" result="shape"/>
</filter>
<linearGradient id="paint0_linear_45761_3794" x1="18.0279" y1="8.5" x2="20.7169" y2="26.1645" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4" style="stop-color:white;stop-opacity:0.4;"/>
<stop offset="1" stop-color="#F7D7FF" stop-opacity="0.5" style="stop-color:#F7D7FF;stop-color:color(display-p3 0.9683 0.8416 1.0000);stop-opacity:0.5;"/>
</linearGradient>
</defs>
</svg>
