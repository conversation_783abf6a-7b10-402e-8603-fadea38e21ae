package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	fmt.Println("Testing GraphQL API...")
	
	// Test health endpoint
	resp, err := http.Get("http://127.0.0.1:8080/api/cdn-service/graphql/healthz")
	if err != nil {
		fmt.Printf("Health check failed: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("Health check: %s\n", string(body))
	
	// Test GraphQL health query
	query := `{"query": "query { health }"}`
	req, _ := http.NewRequest("POST", "http://127.0.0.1:8080/api/cdn-service/graphql", bytes.NewBufferString(query))
	req.Header.Set("Content-Type", "application/json")
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp2, err := client.Do(req)
	if err != nil {
		fmt.Printf("GraphQL health failed: %v\n", err)
		return
	}
	defer resp2.Body.Close()
	
	body2, _ := io.ReadAll(resp2.Body)
	fmt.Printf("GraphQL health: %s\n", string(body2))
	
	fmt.Println("Test completed!")
}
