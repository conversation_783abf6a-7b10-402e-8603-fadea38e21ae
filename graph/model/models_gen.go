// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
)

type DownloadResponse struct {
	Success   bool    `json:"success"`
	Message   string  `json:"message"`
	URL       *string `json:"url,omitempty"`
	ExpiresAt *string `json:"expiresAt,omitempty"`
}

type FileFilter struct {
	FileType       *FileType   `json:"fileType,omitempty"`
	Status         *FileStatus `json:"status,omitempty"`
	Tags           []string    `json:"tags,omitempty"`
	UploadedAfter  *string     `json:"uploadedAfter,omitempty"`
	UploadedBefore *string     `json:"uploadedBefore,omitempty"`
}

type FileListResponse struct {
	Files           []*FileMetadata `json:"files"`
	TotalCount      int             `json:"totalCount"`
	HasNextPage     bool            `json:"hasNextPage"`
	HasPreviousPage bool            `json:"hasPreviousPage"`
}

type FileMetadata struct {
	ID           string     `json:"id"`
	Filename     string     `json:"filename"`
	OriginalName string     `json:"originalName"`
	FileType     FileType   `json:"fileType"`
	MimeType     string     `json:"mimeType"`
	Size         int        `json:"size"`
	Status       FileStatus `json:"status"`
	UploadedAt   string     `json:"uploadedAt"`
	UpdatedAt    string     `json:"updatedAt"`
	PublicURL    *string    `json:"publicURL,omitempty"`
	CdnURL       *string    `json:"cdnURL,omitempty"`
	Tags         []string   `json:"tags,omitempty"`
	Metadata     *string    `json:"metadata,omitempty"`
}

type Mutation struct {
}

type PaginationInput struct {
	Limit  *int `json:"limit,omitempty"`
	Offset *int `json:"offset,omitempty"`
}

type Query struct {
}

type UploadInput struct {
	Filename        string   `json:"filename"`
	FileType        FileType `json:"fileType"`
	MimeType        string   `json:"mimeType"`
	Size            int      `json:"size"`
	Tags            []string `json:"tags,omitempty"`
	Metadata        *string  `json:"metadata,omitempty"`
	UseSignedUpload *bool    `json:"useSignedUpload,omitempty"`
}

type UploadResponse struct {
	Success   bool          `json:"success"`
	Message   string        `json:"message"`
	File      *FileMetadata `json:"file,omitempty"`
	UploadURL *string       `json:"uploadURL,omitempty"`
}

type FileStatus string

const (
	FileStatusUploading  FileStatus = "UPLOADING"
	FileStatusProcessing FileStatus = "PROCESSING"
	FileStatusReady      FileStatus = "READY"
	FileStatusError      FileStatus = "ERROR"
)

var AllFileStatus = []FileStatus{
	FileStatusUploading,
	FileStatusProcessing,
	FileStatusReady,
	FileStatusError,
}

func (e FileStatus) IsValid() bool {
	switch e {
	case FileStatusUploading, FileStatusProcessing, FileStatusReady, FileStatusError:
		return true
	}
	return false
}

func (e FileStatus) String() string {
	return string(e)
}

func (e *FileStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = FileStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid FileStatus", str)
	}
	return nil
}

func (e FileStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *FileStatus) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e FileStatus) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type FileType string

const (
	FileTypeImage FileType = "IMAGE"
	FileTypeVideo FileType = "VIDEO"
)

var AllFileType = []FileType{
	FileTypeImage,
	FileTypeVideo,
}

func (e FileType) IsValid() bool {
	switch e {
	case FileTypeImage, FileTypeVideo:
		return true
	}
	return false
}

func (e FileType) String() string {
	return string(e)
}

func (e *FileType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = FileType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid FileType", str)
	}
	return nil
}

func (e FileType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *FileType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e FileType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type URLType string

const (
	URLTypePublic URLType = "PUBLIC"
	URLTypeSigned URLType = "SIGNED"
)

var AllURLType = []URLType{
	URLTypePublic,
	URLTypeSigned,
}

func (e URLType) IsValid() bool {
	switch e {
	case URLTypePublic, URLTypeSigned:
		return true
	}
	return false
}

func (e URLType) String() string {
	return string(e)
}

func (e *URLType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = URLType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid URLType", str)
	}
	return nil
}

func (e URLType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *URLType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e URLType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
