#!/bin/bash

# XBIT CDN Service - cURL Examples
# Ví dụ sử dụng GraphQL API với cURL

BASE_URL="http://localhost:8080"
GRAPHQL_URL="$BASE_URL/api/cdn-service/graphql"
AUTH_URL="$BASE_URL/auth/login"

echo "🚀 XBIT CDN Service - GraphQL API Examples with cURL"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "\n${YELLOW}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 1. Health Check
print_step "1. Health Check"
curl -s "$BASE_URL/api/cdn-service/graphql/healthz" | jq '.' || echo "Health check response"

# 2. GraphQL Health Query
print_step "2. GraphQL Health Query"
curl -s -X POST "$GRAPHQL_URL" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { health }"}' | jq '.'

# 3. Authentication
print_step "3. Authentication"
AUTH_RESPONSE=$(curl -s -X POST "$AUTH_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

echo "$AUTH_RESPONSE" | jq '.'

# Extract token
TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.token // empty')

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
    print_error "Failed to get authentication token"
    exit 1
fi

print_success "Authentication successful"

# 4. Upload File (Signed URL)
print_step "4. Upload File (Signed URL)"
UPLOAD_RESPONSE=$(curl -s -X POST "$GRAPHQL_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "query": "mutation UploadFile($input: UploadInput!) { uploadFile(input: $input) { success message uploadURL file { id filename status } } }",
    "variables": {
      "input": {
        "filename": "test-image.jpg",
        "fileType": "IMAGE",
        "mimeType": "image/jpeg",
        "size": 1024000,
        "tags": ["test", "curl-example"],
        "useSignedUpload": true
      }
    }
  }')

echo "$UPLOAD_RESPONSE" | jq '.'

# Extract file ID and upload URL
FILE_ID=$(echo "$UPLOAD_RESPONSE" | jq -r '.data.uploadFile.file.id // empty')
UPLOAD_URL=$(echo "$UPLOAD_RESPONSE" | jq -r '.data.uploadFile.uploadURL // empty')

if [ -n "$FILE_ID" ] && [ "$FILE_ID" != "null" ]; then
    print_success "Upload request created successfully (File ID: $FILE_ID)"
    
    # If we had an actual file, we would upload it like this:
    # curl -X PUT -T "test-image.jpg" "$UPLOAD_URL"
    
    # 5. Get File Info
    print_step "5. Get File Info"
    curl -s -X POST "$GRAPHQL_URL" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $TOKEN" \
      -d "{
        \"query\": \"query GetFile(\$id: ID!) { file(id: \$id) { id filename originalName fileType mimeType size status uploadedAt publicURL cdnURL tags } }\",
        \"variables\": { \"id\": \"$FILE_ID\" }
      }" | jq '.'

    # 6. Generate Download URL
    print_step "6. Generate Download URL"
    curl -s -X POST "$GRAPHQL_URL" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $TOKEN" \
      -d "{
        \"query\": \"query GenerateDownloadURL(\$id: ID!, \$urlType: URLType, \$expiresIn: Int) { downloadURL(id: \$id, urlType: \$urlType, expiresIn: \$expiresIn) { success message url expiresAt } }\",
        \"variables\": { 
          \"id\": \"$FILE_ID\", 
          \"urlType\": \"PUBLIC\", 
          \"expiresIn\": 3600 
        }
      }" | jq '.'
else
    print_error "Failed to create upload request"
fi

# 7. List Files
print_step "7. List Files"
curl -s -X POST "$GRAPHQL_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "query": "query ListFiles($filter: FileFilter, $pagination: PaginationInput) { files(filter: $filter, pagination: $pagination) { files { id filename fileType status uploadedAt publicURL cdnURL } totalCount hasNextPage hasPreviousPage } }",
    "variables": {
      "filter": { "fileType": "IMAGE" },
      "pagination": { "limit": 10, "offset": 0 }
    }
  }' | jq '.'

# 8. GraphQL Schema Introspection
print_step "8. GraphQL Schema Introspection"
curl -s -X POST "$GRAPHQL_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query IntrospectionQuery { __schema { types { name kind description } } }"
  }' | jq '.data.__schema.types | length' | xargs -I {} echo "Found {} GraphQL types"

print_step "9. Available GraphQL Types"
curl -s -X POST "$GRAPHQL_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query IntrospectionQuery { __schema { types { name kind } } }"
  }' | jq -r '.data.__schema.types[] | select(.kind == "OBJECT" or .kind == "ENUM") | "- \(.name) (\(.kind))"' | head -10

echo -e "\n${GREEN}🎉 All examples completed!${NC}"

# Additional examples for specific use cases

echo -e "\n${YELLOW}📋 Additional Examples:${NC}"

echo -e "\n${YELLOW}Upload with metadata:${NC}"
cat << 'EOF'
curl -X POST "$GRAPHQL_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "query": "mutation UploadFile($input: UploadInput!) { uploadFile(input: $input) { success uploadURL file { id } } }",
    "variables": {
      "input": {
        "filename": "profile-pic.jpg",
        "fileType": "IMAGE",
        "mimeType": "image/jpeg",
        "size": 2048000,
        "tags": ["profile", "user", "avatar"],
        "metadata": "{\"userId\": \"123\", \"category\": \"profile\"}",
        "useSignedUpload": true
      }
    }
  }'
EOF

echo -e "\n${YELLOW}Filter files by status:${NC}"
cat << 'EOF'
curl -X POST "$GRAPHQL_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "query": "query ListFiles($filter: FileFilter) { files(filter: $filter) { files { id filename status } totalCount } }",
    "variables": {
      "filter": { "status": "READY", "fileType": "IMAGE" }
    }
  }'
EOF

echo -e "\n${YELLOW}Update file metadata:${NC}"
cat << 'EOF'
curl -X POST "$GRAPHQL_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "query": "mutation UpdateFileMetadata($id: ID!, $tags: [String!], $metadata: String) { updateFileMetadata(id: $id, tags: $tags, metadata: $metadata) { id tags metadata } }",
    "variables": {
      "id": "your-file-id",
      "tags": ["updated", "new-tag"],
      "metadata": "{\"updated\": true, \"version\": 2}"
    }
  }'
EOF

echo -e "\n${YELLOW}Delete file:${NC}"
cat << 'EOF'
curl -X POST "$GRAPHQL_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "query": "mutation DeleteFile($id: ID!) { deleteFile(id: $id) }",
    "variables": { "id": "your-file-id" }
  }'
EOF

echo -e "\n${GREEN}💡 Tips:${NC}"
echo "- Use jq to format JSON responses: | jq '.'"
echo "- Save token to variable for reuse: TOKEN=\$(curl ... | jq -r '.token')"
echo "- Use signed URLs for large files"
echo "- Always check the 'success' field in responses"
echo "- Use pagination for large file lists"
