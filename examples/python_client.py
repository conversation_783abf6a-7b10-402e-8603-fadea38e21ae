"""
XBIT CDN Service - Python GraphQL Client Example

<PERSON><PERSON>ụ sử dụng GraphQL API để upload và download ảnh
"""

import requests
import json
import os
from typing import Dict, List, Optional, Any


class XbitCDNClient:
    def __init__(self, base_url: str, token: Optional[str] = None):
        self.base_url = base_url
        self.graphql_url = f"{base_url}/api/cdn-service/graphql"
        self.auth_url = f"{base_url}/auth/login"
        self.token = token

    def login(self, username: str, password: str) -> Dict[str, Any]:
        """Đăng nhập và lấy JWT token"""
        response = requests.post(
            self.auth_url,
            json={"username": username, "password": password}
        )
        response.raise_for_status()
        
        data = response.json()
        self.token = data["token"]
        return data

    def graphql_request(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Thực hiện GraphQL request"""
        headers = {"Content-Type": "application/json"}
        
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"

        payload = {"query": query}
        if variables:
            payload["variables"] = variables

        response = requests.post(
            self.graphql_url,
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        
        data = response.json()
        
        if "errors" in data:
            raise Exception(f"GraphQL errors: {data['errors']}")
        
        return data["data"]

    def health_check(self) -> str:
        """Kiểm tra health của service"""
        query = "query { health }"
        response = self.graphql_request(query)
        return response["health"]

    def upload_file(self, file_path: str, tags: Optional[List[str]] = None, 
                   metadata: Optional[str] = None, use_signed_upload: bool = True) -> Dict[str, Any]:
        """Upload file sử dụng signed URL method"""
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        
        # Determine file type and mime type
        file_ext = os.path.splitext(file_name)[1].lower()
        if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
            file_type = "IMAGE"
            mime_type = f"image/{file_ext[1:]}" if file_ext != '.jpg' else "image/jpeg"
        elif file_ext in ['.mp4', '.avi', '.mov']:
            file_type = "VIDEO"
            mime_type = f"video/{file_ext[1:]}"
        else:
            raise ValueError(f"Unsupported file type: {file_ext}")

        # Step 1: Create upload request
        upload_mutation = """
            mutation UploadFile($input: UploadInput!) {
                uploadFile(input: $input) {
                    success
                    message
                    uploadURL
                    file {
                        id
                        filename
                        status
                        publicURL
                        cdnURL
                    }
                }
            }
        """

        variables = {
            "input": {
                "filename": file_name,
                "fileType": file_type,
                "mimeType": mime_type,
                "size": file_size,
                "tags": tags or [],
                "metadata": metadata,
                "useSignedUpload": use_signed_upload
            }
        }

        upload_response = self.graphql_request(upload_mutation, variables)
        upload_data = upload_response["uploadFile"]
        
        if not upload_data["success"]:
            raise Exception(upload_data["message"])

        upload_url = upload_data.get("uploadURL")
        file_metadata = upload_data["file"]

        if use_signed_upload and upload_url:
            # Step 2: Upload file to signed URL
            with open(file_path, 'rb') as f:
                upload_file_response = requests.put(
                    upload_url,
                    data=f,
                    headers={"Content-Type": mime_type}
                )
                upload_file_response.raise_for_status()

            # Step 3: Complete upload
            complete_upload_mutation = """
                mutation CompleteUpload($id: ID!) {
                    completeUpload(id: $id) {
                        id
                        filename
                        status
                        publicURL
                        cdnURL
                    }
                }
            """

            complete_response = self.graphql_request(
                complete_upload_mutation, 
                {"id": file_metadata["id"]}
            )
            return complete_response["completeUpload"]

        return file_metadata

    def get_file(self, file_id: str) -> Dict[str, Any]:
        """Lấy thông tin file theo ID"""
        query = """
            query GetFile($id: ID!) {
                file(id: $id) {
                    id
                    filename
                    originalName
                    fileType
                    mimeType
                    size
                    status
                    uploadedAt
                    updatedAt
                    publicURL
                    cdnURL
                    tags
                    metadata
                }
            }
        """
        
        response = self.graphql_request(query, {"id": file_id})
        return response["file"]

    def list_files(self, file_filter: Optional[Dict] = None, 
                  pagination: Optional[Dict] = None) -> Dict[str, Any]:
        """Liệt kê files với filter và pagination"""
        query = """
            query ListFiles($filter: FileFilter, $pagination: PaginationInput) {
                files(filter: $filter, pagination: $pagination) {
                    files {
                        id
                        filename
                        fileType
                        status
                        uploadedAt
                        publicURL
                        cdnURL
                        tags
                    }
                    totalCount
                    hasNextPage
                    hasPreviousPage
                }
            }
        """
        
        variables = {
            "filter": file_filter or {},
            "pagination": pagination or {"limit": 20, "offset": 0}
        }
        
        response = self.graphql_request(query, variables)
        return response["files"]

    def generate_download_url(self, file_id: str, url_type: str = "PUBLIC", 
                            expires_in: int = 3600) -> Dict[str, Any]:
        """Tạo download URL"""
        query = """
            query GenerateDownloadURL($id: ID!, $urlType: URLType, $expiresIn: Int) {
                downloadURL(id: $id, urlType: $urlType, expiresIn: $expiresIn) {
                    success
                    message
                    url
                    expiresAt
                }
            }
        """
        
        variables = {
            "id": file_id,
            "urlType": url_type,
            "expiresIn": expires_in
        }
        
        response = self.graphql_request(query, variables)
        return response["downloadURL"]

    def delete_file(self, file_id: str) -> bool:
        """Xóa file"""
        mutation = """
            mutation DeleteFile($id: ID!) {
                deleteFile(id: $id)
            }
        """
        
        response = self.graphql_request(mutation, {"id": file_id})
        return response["deleteFile"]

    def update_file_metadata(self, file_id: str, tags: Optional[List[str]] = None, 
                           metadata: Optional[str] = None) -> Dict[str, Any]:
        """Cập nhật metadata của file"""
        mutation = """
            mutation UpdateFileMetadata($id: ID!, $tags: [String!], $metadata: String) {
                updateFileMetadata(id: $id, tags: $tags, metadata: $metadata) {
                    id
                    tags
                    metadata
                }
            }
        """
        
        variables = {"id": file_id}
        if tags is not None:
            variables["tags"] = tags
        if metadata is not None:
            variables["metadata"] = metadata
        
        response = self.graphql_request(mutation, variables)
        return response["updateFileMetadata"]


def main():
    """Ví dụ sử dụng client"""
    client = XbitCDNClient("http://localhost:8080")
    
    try:
        # 1. Login
        print("1. Logging in...")
        login_result = client.login("admin", "admin123")
        print(f"✅ Login successful: {login_result['user']['username']}")
        
        # 2. Health check
        print("2. Health check...")
        health = client.health_check()
        print(f"✅ Health: {health}")
        
        # 3. Upload file (uncomment if you have a test image)
        # print("3. Uploading file...")
        # uploaded_file = client.upload_file(
        #     "test-image.jpg",
        #     tags=["example", "test"],
        #     metadata=json.dumps({"source": "python-client"})
        # )
        # print(f"✅ File uploaded: {uploaded_file['filename']} (ID: {uploaded_file['id']})")
        # 
        # # 4. Get file info
        # print("4. Getting file info...")
        # file_info = client.get_file(uploaded_file["id"])
        # print(f"✅ File info: {file_info['filename']} - {file_info['status']}")
        # 
        # # 5. Generate download URL
        # print("5. Generating download URL...")
        # download_url = client.generate_download_url(uploaded_file["id"], "PUBLIC")
        # print(f"✅ Download URL: {download_url['url']}")
        
        # 6. List files
        print("6. Listing files...")
        files = client.list_files(
            file_filter={"fileType": "IMAGE"},
            pagination={"limit": 10, "offset": 0}
        )
        print(f"✅ Found {files['totalCount']} files")
        for file in files['files'][:3]:  # Show first 3 files
            print(f"   - {file['filename']} ({file['status']})")
        
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
