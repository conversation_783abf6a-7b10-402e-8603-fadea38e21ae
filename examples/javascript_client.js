/**
 * XBIT CDN Service - JavaScript GraphQL Client Example
 * 
 * <PERSON><PERSON> dụ sử dụng GraphQL API để upload và download ảnh
 */

class XbitCDNClient {
  constructor(baseURL, token = null) {
    this.baseURL = baseURL;
    this.graphqlURL = `${baseURL}/api/cdn-service/graphql`;
    this.authURL = `${baseURL}/auth/login`;
    this.token = token;
  }

  // Authentication
  async login(username, password) {
    const response = await fetch(this.authURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.statusText}`);
    }

    const data = await response.json();
    this.token = data.token;
    return data;
  }

  // GraphQL request helper
  async graphqlRequest(query, variables = {}) {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    const response = await fetch(this.graphqlURL, {
      method: 'POST',
      headers,
      body: JSON.stringify({ query, variables }),
    });

    if (!response.ok) {
      throw new Error(`GraphQL request failed: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(data.errors)}`);
    }

    return data.data;
  }

  // Health check
  async healthCheck() {
    const query = `query { health }`;
    return await this.graphqlRequest(query);
  }

  // Upload file (signed URL method)
  async uploadFile(file, options = {}) {
    const {
      tags = [],
      metadata = null,
      useSignedUpload = true
    } = options;

    // Step 1: Create upload request
    const uploadMutation = `
      mutation UploadFile($input: UploadInput!) {
        uploadFile(input: $input) {
          success
          message
          uploadURL
          file {
            id
            filename
            status
            publicURL
            cdnURL
          }
        }
      }
    `;

    const variables = {
      input: {
        filename: file.name,
        fileType: file.type.startsWith('image/') ? 'IMAGE' : 'VIDEO',
        mimeType: file.type,
        size: file.size,
        tags,
        metadata,
        useSignedUpload
      }
    };

    const uploadResponse = await this.graphqlRequest(uploadMutation, variables);
    
    if (!uploadResponse.uploadFile.success) {
      throw new Error(uploadResponse.uploadFile.message);
    }

    const { uploadURL, file: fileMetadata } = uploadResponse.uploadFile;

    if (useSignedUpload && uploadURL) {
      // Step 2: Upload file to signed URL
      const uploadFileResponse = await fetch(uploadURL, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (!uploadFileResponse.ok) {
        throw new Error(`File upload failed: ${uploadFileResponse.statusText}`);
      }

      // Step 3: Complete upload
      const completeUploadMutation = `
        mutation CompleteUpload($id: ID!) {
          completeUpload(id: $id) {
            id
            filename
            status
            publicURL
            cdnURL
          }
        }
      `;

      const completeResponse = await this.graphqlRequest(completeUploadMutation, {
        id: fileMetadata.id
      });

      return completeResponse.completeUpload;
    }

    return fileMetadata;
  }

  // Get file by ID
  async getFile(id) {
    const query = `
      query GetFile($id: ID!) {
        file(id: $id) {
          id
          filename
          originalName
          fileType
          mimeType
          size
          status
          uploadedAt
          updatedAt
          publicURL
          cdnURL
          tags
          metadata
        }
      }
    `;

    const response = await this.graphqlRequest(query, { id });
    return response.file;
  }

  // List files
  async listFiles(filter = {}, pagination = { limit: 20, offset: 0 }) {
    const query = `
      query ListFiles($filter: FileFilter, $pagination: PaginationInput) {
        files(filter: $filter, pagination: $pagination) {
          files {
            id
            filename
            fileType
            status
            uploadedAt
            publicURL
            cdnURL
            tags
          }
          totalCount
          hasNextPage
          hasPreviousPage
        }
      }
    `;

    const response = await this.graphqlRequest(query, { filter, pagination });
    return response.files;
  }

  // Generate download URL
  async generateDownloadURL(id, urlType = 'PUBLIC', expiresIn = 3600) {
    const query = `
      query GenerateDownloadURL($id: ID!, $urlType: URLType, $expiresIn: Int) {
        downloadURL(id: $id, urlType: $urlType, expiresIn: $expiresIn) {
          success
          message
          url
          expiresAt
        }
      }
    `;

    const response = await this.graphqlRequest(query, { id, urlType, expiresIn });
    return response.downloadURL;
  }

  // Delete file
  async deleteFile(id) {
    const mutation = `
      mutation DeleteFile($id: ID!) {
        deleteFile(id: $id)
      }
    `;

    const response = await this.graphqlRequest(mutation, { id });
    return response.deleteFile;
  }

  // Update file metadata
  async updateFileMetadata(id, tags, metadata) {
    const mutation = `
      mutation UpdateFileMetadata($id: ID!, $tags: [String!], $metadata: String) {
        updateFileMetadata(id: $id, tags: $tags, metadata: $metadata) {
          id
          tags
          metadata
        }
      }
    `;

    const response = await this.graphqlRequest(mutation, { id, tags, metadata });
    return response.updateFileMetadata;
  }
}

// Usage Examples
async function examples() {
  const client = new XbitCDNClient('http://localhost:8080');

  try {
    // 1. Login
    console.log('1. Logging in...');
    await client.login('admin', 'admin123');
    console.log('✅ Login successful');

    // 2. Health check
    console.log('2. Health check...');
    const health = await client.healthCheck();
    console.log('✅ Health:', health);

    // 3. Upload file (example with File object)
    // const fileInput = document.getElementById('fileInput');
    // const file = fileInput.files[0];
    // 
    // if (file) {
    //   console.log('3. Uploading file...');
    //   const uploadedFile = await client.uploadFile(file, {
    //     tags: ['example', 'test'],
    //     metadata: JSON.stringify({ source: 'web-app' })
    //   });
    //   console.log('✅ File uploaded:', uploadedFile);
    //
    //   // 4. Get file info
    //   console.log('4. Getting file info...');
    //   const fileInfo = await client.getFile(uploadedFile.id);
    //   console.log('✅ File info:', fileInfo);
    //
    //   // 5. Generate download URL
    //   console.log('5. Generating download URL...');
    //   const downloadURL = await client.generateDownloadURL(uploadedFile.id, 'PUBLIC');
    //   console.log('✅ Download URL:', downloadURL);
    // }

    // 6. List files
    console.log('6. Listing files...');
    const files = await client.listFiles(
      { fileType: 'IMAGE' },
      { limit: 10, offset: 0 }
    );
    console.log('✅ Files:', files);

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Export for use in Node.js or browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = XbitCDNClient;
} else {
  window.XbitCDNClient = XbitCDNClient;
}

// Run examples if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  examples();
}
