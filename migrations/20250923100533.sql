-- Create "files" table
CREATE TABLE "public"."files" (
  "id" character varying(255) NOT NULL,
  "filename" character varying(255) NOT NULL,
  "original_name" character varying(255) NOT NULL,
  "file_type" character varying(50) NOT NULL,
  "mime_type" character varying(100) NOT NULL,
  "size" bigint NOT NULL,
  "status" character varying(50) NOT NULL DEFAULT 'UPLOADING',
  "uploaded_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "public_url" text NULL,
  "cdn_url" text NULL,
  "tags" text[] NULL,
  "metadata" text NULL,
  "user_id" character varying(255) NULL,
  PRIMARY KEY ("id")
);
